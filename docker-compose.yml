services:
  application:
    image: 992382651472.dkr.ecr.ap-southeast-2.amazonaws.com/job-swipe/server:latest
    container_name: main-server
    ports:
      - "8080:8080"
      - "8888:8888"
    depends_on:
      - redis
    environment:
      - SPRING_PROFILE=${SPRING_PROFILE}
      - SPRING_DATASOURCE_URL=${SPRING_DATASOURCE_URL-********************************************}
      - SPRING_DATASOURCE_USERNAME=${SPRING_DATASOURCE_USERNAME-qh47Qsmu19JJRuMq}
      - SPRING_DATASOURCE_PASSWORD=${SPRING_DATASOURCE_PASSWORD-TwwlZL9j10wyziG3}
      - SPRING_REDIS_HOST=${SPRING_REDIS_HOST-db-redis}
      - SPRING_REDIS_PORT=${SPRING_REDIS_PORT-6379}
      - SPRING_REDIS_USERNAME=${SPRING_REDIS_USERNAME}
      - SPRING_REDIS_PASSWORD=${SPRING_REDIS_PASSWORD}
      - SPRING_REDIS_SSL_ENABLED=${SPRING_REDIS_SSL_ENABLED-false}
      - SPRING_MAIL_HOST=${SPRING_MAIL_HOST-smtp.gmail.com}
      - SPRING_MAIL_PORT=${SPRING_MAIL_PORT-587}
      - SPRING_MAIL_USERNAME=${SPRING_MAIL_USERNAME}
      - SPRING_MAIL_PASSWORD=${SPRING_MAIL_PASSWORD}
      - JWT_ACCESS_TOKEN_SECRET_KEY=${JWT_ACCESS_TOKEN_SECRET_KEY}
      - JWT_ACCESS_TOKEN_EXPIRATION_MS=${JWT_ACCESS_TOKEN_EXPIRATION_MS}
      - JWT_REFRESH_TOKEN_SECRET_KEY=${JWT_REFRESH_TOKEN_SECRET_KEY}
      - JWT_REFRESH_TOKEN_EXPIRATION_MS=${JWT_REFRESH_TOKEN_EXPIRATION_MS}
      - RESET_PASSWORD_CODE_EXPIRATION_MS=${RESET_PASSWORD_CODE_EXPIRATION_MS}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET_KEY=${S3_SECRET_KEY}
      - LLAMA_INDEX_API_KEY=${LLAMA_INDEX_API_KEY}
      - LLAMA_INDEX_AGENT_ID=${LLAMA_INDEX_AGENT_ID}
      - LLAMA_INDEX_PROJECT_ID=${LLAMA_INDEX_PROJECT_ID}
      - MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER=${MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER}
      - FIREBASE_TYPE=${FIREBASE_TYPE}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID}
      - FIREBASE_AUTH_URI=${FIREBASE_AUTH_URI}
      - FIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI}
      - FIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL}
      - FIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL}
      - FIREBASE_UNIVERSE_DOMAIN=${FIREBASE_UNIVERSE_DOMAIN}
    env_file:
      - .env
    networks:
      - job-swipe-networks

  redis:
    image: redis:6.2-alpine
    container_name: db-redis
    restart: always
    ports:
      - "6003:6379"
    volumes:
      - redis:/data
    networks:
      - job-swipe-networks

volumes:
  redis:
    driver: local

networks:
  job-swipe-networks:
    driver: bridge
