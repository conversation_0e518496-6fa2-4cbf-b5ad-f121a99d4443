# 🔥 Firebase Setup Guide cho Job Swipe User Mobile

## **Bước 1: Tạo Firebase Project**

1. Vào [Firebase Console](https://console.firebase.google.com)
2. Click **"Create a project"**
3. Project name: `job-swipe-chat-service` (match với server)
4. Enable Google Analytics: **Yes** (recommended)
5. Choose Analytics account: **Default**
6. Click **"Create project"**

## **Bước 2: Setup Android Apps**

### **2.1 Add Development App:**
1. Click **"Add app"** → **Android**
2. **Package name:** `com.dhv.job_swipe.dev`
3. **App nickname:** `Job Swipe User Dev`
4. **Debug signing certificate SHA-1:** (optional for now)
5. Click **"Register app"**
6. **Download `google-services.json`**
7. Rename to: `google-services-dev.json`

### **2.2 Add Production App:**
1. Click **"Add app"** → **Android**
2. **Package name:** `com.dhv.job_swipe.prod`
3. **App nickname:** `Job Swipe User Prod`
4. Download và rename to: `google-services-prod.json`

### **2.3 Place Config Files:**
```bash
# Tạo thư mục cho multiple flavors
mkdir -p android/app/src/development
mkdir -p android/app/src/production

# Copy files
cp google-services-dev.json android/app/src/development/google-services.json
cp google-services-prod.json android/app/src/production/google-services.json
```

## **Bước 3: Setup iOS App**

### **3.1 Check Bundle ID:**
```bash
# Mở ios/Runner.xcworkspace trong Xcode
# Check Bundle Identifier trong project settings
# Thường sẽ là: com.dhv.jobSwipe hoặc tương tự
```

### **3.2 Add iOS App:**
1. Click **"Add app"** → **iOS**
2. **Bundle ID:** `[bundle_id_from_xcode]`
3. **App nickname:** `Job Swipe User iOS`
4. **Download `GoogleService-Info.plist`**
5. **Drag file vào Xcode project** (ios/Runner folder)

## **Bước 4: Enable Firebase Services**

### **4.1 Authentication:**
```bash
1. Go to Authentication → Sign-in method
2. Enable "Custom token"
3. Save
```

### **4.2 Realtime Database:**
```bash
1. Go to Realtime Database → Create database
2. Choose location: asia-southeast1 (Singapore)
3. Start in "Test mode" (for now)
4. Database URL sẽ là: https://job-swipe-chat-service-xxxxx-default-rtdb.asia-southeast1.firebasedatabase.app
```

### **4.3 Cloud Messaging:**
```bash
1. Go to Cloud Messaging
2. Tự động enabled
3. Note down "Server key" cho server config
```

## **Bước 5: Update Android Build Config**

### **5.1 Google Services Plugin (✅ Đã có):**
```gradle
// android/build.gradle - ✅ Đã có
classpath 'com.google.gms:google-services:4.3.15'

// android/app/build.gradle - ✅ Đã thêm
id "com.google.gms.google-services"
```

## **Bước 6: Database Security Rules**

### **6.1 Setup Realtime Database Rules:**
```javascript
{
  "rules": {
    "chat_rooms": {
      "$roomId": {
        ".read": "auth != null && (root.child('chat_rooms').child($roomId).child('participant_ids').val().contains(auth.uid))",
        ".write": "auth != null && (root.child('chat_rooms').child($roomId).child('participant_ids').val().contains(auth.uid))"
      }
    },
    "messages": {
      "$roomId": {
        ".read": "auth != null && (root.child('chat_rooms').child($roomId).child('participant_ids').val().contains(auth.uid))",
        ".write": "auth != null && (root.child('chat_rooms').child($roomId).child('participant_ids').val().contains(auth.uid))"
      }
    }
  }
}
```

## **Bước 7: Server Configuration**

### **7.1 FCM Server Key:**
```bash
1. Go to Project Settings → Cloud Messaging
2. Copy "Server key"
3. Add to server environment variables:
   FIREBASE_SERVER_KEY=AAAA...
```

### **7.2 Service Account (cho server):**
```bash
1. Go to Project Settings → Service accounts
2. Click "Generate new private key"
3. Download JSON file
4. Add to server config (như đã có trong server code)
```

## **Bước 8: Test Setup**

### **8.1 Test Firebase Connection:**
```bash
# Run app và check logs
flutter run --flavor development

# Logs should show:
# ✅ Firebase initialized successfully
# ✅ FCM Token: [token]
# ✅ Firebase Auth successful for user: [userId]
```

### **8.2 Test FCM Registration:**
```bash
# Login vào app
# Check server logs for FCM token registration
# Test gửi notification từ Firebase Console
```

## **🔑 Summary - Keys bạn cần:**

### **Cho Mobile App:**
- ✅ `google-services.json` files (Android)
- ✅ `GoogleService-Info.plist` (iOS)
- ❌ **KHÔNG cần hardcode keys trong code**

### **Cho Server:**
- 🔑 **FCM Server Key** (để gửi push notifications)
- 🔑 **Service Account JSON** (để admin operations)
- 🔑 **Database URL** (realtime database endpoint)

### **Environment Variables cho Server:**
```bash
FIREBASE_SERVER_KEY=AAAA...
FIREBASE_PROJECT_ID=job-swipe-chat-service
FIREBASE_DATABASE_URL=https://job-swipe-chat-service-xxxxx-default-rtdb.asia-southeast1.firebasedatabase.app
```

## **🚀 Ready to Test!**

Sau khi setup xong:
1. **Mobile app** sẽ tự động connect Firebase qua config files
2. **Server** sẽ gửi notifications qua FCM Server Key
3. **Realtime chat** sẽ hoạt động qua Database URL
4. **Authentication** sẽ dùng custom tokens từ server

**Không cần hardcode bất kỳ key nào trong mobile code!** 🎯


<function_calls>
<invoke name="view">
<parameter name="path">android/build.gradle
