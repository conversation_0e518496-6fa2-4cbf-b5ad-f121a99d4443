# Pull Request Template

## Description

Please include a summary of the changes and the related issue. Highlight any key points or areas of focus.

## Related Issue

Fixes # (issue)

## Type of Change

- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update
- [ ] Other (please specify):

## Checklist

- [ ] My code follows the style guidelines of this project.
- [ ] I have performed a self-review of my code.
- [ ] I have commented my code, particularly in hard-to-understand areas.
- [ ] I have added tests that prove my fix is effective or that my feature works.
- [ ] New and existing unit tests pass locally with my changes.
- [ ] I have updated the documentation where necessary.

## Screenshots (if applicable)

Add screenshots to help explain your changes.

## Additional Notes

Include any other relevant information or context.