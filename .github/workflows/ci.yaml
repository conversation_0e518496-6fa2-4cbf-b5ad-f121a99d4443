name: CI Workflow

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened

jobs:
  build:
    runs-on: ubuntu-latest

    env:
      SPRING_DATASOURCE_URL: ${{ secrets.SPRING_DATASOURCE_URL }}
      SPRING_DATASOURCE_USERNAME: ${{ secrets.SPRING_DATASOURCE_USERNAME }}
      SPRING_DATASOURCE_PASSWORD: ${{ secrets.SPRING_DATASOURCE_PASSWORD }}
      SPRING_REDIS_USERNAME: ${{ secrets.SPRING_REDIS_USERNAME }}
      SPRING_REDIS_PASSWORD: ${{ secrets.SPRING_REDIS_PASSWORD }}
      SPRING_REDIS_HOST: ${{ secrets.SPRING_REDIS_HOST }}
      SPRING_REDIS_PORT: ${{ secrets.SPRING_REDIS_PORT }}
      SPRING_REDIS_SSL_ENABLED: ${{ secrets.SPRING_REDIS_SSL_ENABLED }}
      SPRING_MAIL_HOST: ${{ secrets.SPRING_MAIL_HOST }}
      SPRING_MAIL_PORT: ${{ secrets.SPRING_MAIL_PORT }}
      SPRING_MAIL_USERNAME: ${{ secrets.SPRING_MAIL_USERNAME }}
      SPRING_MAIL_PASSWORD: ${{ secrets.SPRING_MAIL_PASSWORD }}
      JWT_ACCESS_TOKEN_SECRET_KEY: ${{ secrets.JWT_ACCESS_TOKEN_SECRET_KEY }}
      JWT_ACCESS_TOKEN_EXPIRATION_MS: ${{ secrets.JWT_ACCESS_TOKEN_EXPIRATION_MS }}
      JWT_REFRESH_TOKEN_SECRET_KEY: ${{ secrets.JWT_REFRESH_TOKEN_SECRET_KEY }}
      JWT_REFRESH_TOKEN_EXPIRATION_MS: ${{ secrets.JWT_REFRESH_TOKEN_EXPIRATION_MS }}
      RESET_PASSWORD_CODE_EXPIRATION_MS: ${{ secrets.RESET_PASSWORD_CODE_EXPIRATION_MS }}
      S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
      S3_ACCESS_KEY: ${{ secrets.S3_ACCESS_KEY }}
      S3_SECRET_KEY: ${{ secrets.S3_SECRET_KEY }}
      LLAMA_INDEX_API_KEY: ${{ secrets.LLAMA_INDEX_API_KEY }}
      LLAMA_INDEX_AGENT_ID: ${{ secrets.LLAMA_INDEX_AGENT_ID }}
      MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER: ${{ secrets.MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER }}
      SPRING_PROFILE: ${{ secrets.SPRING_PROFILE }}
      FIREBASE_TYPE: ${{ secrets.FIREBASE_TYPE }}
      FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      FIREBASE_PRIVATE_KEY_ID: ${{ secrets.FIREBASE_PRIVATE_KEY_ID }}
      FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
      FIREBASE_CLIENT_EMAIL: ${{ secrets.FIREBASE_CLIENT_EMAIL }}
      FIREBASE_CLIENT_ID: ${{ secrets.FIREBASE_CLIENT_ID }}
      FIREBASE_AUTH_URI: ${{ secrets.FIREBASE_AUTH_URI }}
      FIREBASE_TOKEN_URI: ${{ secrets.FIREBASE_TOKEN_URI }}
      FIREBASE_AUTH_PROVIDER_X509_CERT_URL: ${{ secrets.FIREBASE_AUTH_PROVIDER_X509_CERT_URL }}
      FIREBASE_CLIENT_X509_CERT_URL: ${{ secrets.FIREBASE_CLIENT_X509_CERT_URL }}
      FIREBASE_UNIVERSE_DOMAIN: ${{ secrets.FIREBASE_UNIVERSE_DOMAIN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Install dependencies
        run: mvn install -DskipTests

      - name: Run tests
        run: |
          mvn test \
            -DSPRING_DATASOURCE_URL=${SPRING_DATASOURCE_URL} \
            -DSPRING_DATASOURCE_USERNAME=${SPRING_DATASOURCE_USERNAME} \
            -DSPRING_DATASOURCE_PASSWORD=${SPRING_DATASOURCE_PASSWORD} \
            -DSPRING_REDIS_USERNAME=${SPRING_REDIS_USERNAME} \
            -DSPRING_REDIS_PASSWORD=${SPRING_REDIS_PASSWORD} \
            -DSPRING_REDIS_HOST=${SPRING_REDIS_HOST} \
            -DSPRING_REDIS_PORT=${SPRING_REDIS_PORT} \
            -DSPRING_REDIS_SSL_ENABLED=${SPRING_REDIS_SSL_ENABLED} \
            -DSPRING_MAIL_HOST=${SPRING_MAIL_HOST} \
            -DSPRING_MAIL_PORT=${SPRING_MAIL_PORT} \
            -DSPRING_MAIL_USERNAME=${SPRING_MAIL_USERNAME} \
            -DSPRING_MAIL_PASSWORD=${SPRING_MAIL_PASSWORD} \
            -DJWT_ACCESS_TOKEN_SECRET_KEY=${JWT_ACCESS_TOKEN_SECRET_KEY} \
            -DJWT_ACCESS_TOKEN_EXPIRATION_MS=${JWT_ACCESS_TOKEN_EXPIRATION_MS} \
            -DJWT_REFRESH_TOKEN_SECRET_KEY=${JWT_REFRESH_TOKEN_SECRET_KEY} \
            -DJWT_REFRESH_TOKEN_EXPIRATION_MS=${JWT_REFRESH_TOKEN_EXPIRATION_MS} \
            -DRESET_PASSWORD_CODE_EXPIRATION_MS=${RESET_PASSWORD_CODE_EXPIRATION_MS} \
            -DS3_BUCKET_NAME=${S3_BUCKET_NAME} \
            -DS3_ACCESS_KEY=${S3_ACCESS_KEY} \
            -DS3_SECRET_KEY=${S3_SECRET_KEY} \
            -DLLAMA_INDEX_API_KEY=${LLAMA_INDEX_API_KEY} \
            -DLLAMA_INDEX_AGENT_ID=${LLAMA_INDEX_AGENT_ID} \
            -DMAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER=${MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER} \
            -DSPRING_PROFILE=${SPRING_PROFILE} \
            -DFIREBASE_TYPE=${FIREBASE_TYPE} \
            -DFIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID} \
            -DFIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID} \
            -DFIREBASE_PRIVATE_KEY="${FIREBASE_PRIVATE_KEY}" \
            -DFIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL} \
            -DFIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID} \
            -DFIREBASE_AUTH_URI=${FIREBASE_AUTH_URI} \
            -DFIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI} \
            -DFIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL} \
            -DFIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL} \
            -DFIREBASE_UNIVERSE_DOMAIN=${FIREBASE_UNIVERSE_DOMAIN} \
            -Dspring.profiles.active=${SPRING_PROFILE}

      - name: Start application
        run: |
          mvn spring-boot:run \
            -DSPRING_DATASOURCE_URL=${SPRING_DATASOURCE_URL} \
            -DSPRING_DATASOURCE_USERNAME=${SPRING_DATASOURCE_USERNAME} \
            -DSPRING_DATASOURCE_PASSWORD=${SPRING_DATASOURCE_PASSWORD} \
            -DSPRING_REDIS_USERNAME=${SPRING_REDIS_USERNAME} \
            -DSPRING_REDIS_PASSWORD=${SPRING_REDIS_PASSWORD} \
            -DSPRING_REDIS_HOST=${SPRING_REDIS_HOST} \
            -DSPRING_REDIS_PORT=${SPRING_REDIS_PORT} \
            -DSPRING_REDIS_SSL_ENABLED=${SPRING_REDIS_SSL_ENABLED} \
            -DSPRING_MAIL_HOST=${SPRING_MAIL_HOST} \
            -DSPRING_MAIL_PORT=${SPRING_MAIL_PORT} \
            -DSPRING_MAIL_USERNAME=${SPRING_MAIL_USERNAME} \
            -DSPRING_MAIL_PASSWORD=${SPRING_MAIL_PASSWORD} \
            -DJWT_ACCESS_TOKEN_SECRET_KEY=${JWT_ACCESS_TOKEN_SECRET_KEY} \
            -DJWT_ACCESS_TOKEN_EXPIRATION_MS=${JWT_ACCESS_TOKEN_EXPIRATION_MS} \
            -DJWT_REFRESH_TOKEN_SECRET_KEY=${JWT_REFRESH_TOKEN_SECRET_KEY} \
            -DJWT_REFRESH_TOKEN_EXPIRATION_MS=${JWT_REFRESH_TOKEN_EXPIRATION_MS} \
            -DRESET_PASSWORD_CODE_EXPIRATION_MS=${RESET_PASSWORD_CODE_EXPIRATION_MS} \
            -DS3_BUCKET_NAME=${S3_BUCKET_NAME} \
            -DS3_ACCESS_KEY=${S3_ACCESS_KEY} \
            -DS3_SECRET_KEY=${S3_SECRET_KEY} \
            -DLLAMA_INDEX_API_KEY=${LLAMA_INDEX_API_KEY} \
            -DLLAMA_INDEX_AGENT_ID=${LLAMA_INDEX_AGENT_ID} \
            -DMAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER=${MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER} \
            -DSPRING_PROFILE=${SPRING_PROFILE} \
            -DFIREBASE_TYPE=${FIREBASE_TYPE} \
            -DFIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID} \
            -DFIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID} \
            -DFIREBASE_PRIVATE_KEY="${FIREBASE_PRIVATE_KEY}" \
            -DFIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL} \
            -DFIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID} \
            -DFIREBASE_AUTH_URI=${FIREBASE_AUTH_URI} \
            -DFIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI} \
            -DFIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL} \
            -DFIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL} \
            -DFIREBASE_UNIVERSE_DOMAIN=${FIREBASE_UNIVERSE_DOMAIN} \
            -Dspring.profiles.active=${SPRING_PROFILE} > application.log 2>&1 &

      - name: Wait for application to start
        run: |
          for i in {1..30}; do
            if curl --fail http://localhost:8080/api/v1/health-check; then
              echo "Application is up!"
              exit 0
            fi
            echo "Waiting for application to start..."
            sleep 5
          done
          echo "Application failed to start in time."
          exit 1

