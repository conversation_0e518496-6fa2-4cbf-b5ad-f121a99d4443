-- Constants: Restrict constant_type to SYSTEM_ROLE, NOTIFICATION; keep old constraints
CREATE TABLE public.constants (
    constant_id UUID NOT NULL PRIMARY KEY,
    constant_name CHARACTER VARYING(1000) NOT NULL,
    constant_type CHARACTER VARYING(1000) NOT NULL,
    description JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    -- CONSTRAINT unique_constant_type UNIQUE (constant_type),
    -- CONSTRAINT valid_constant_type CHECK (constant_type IN ('SYSTEM_ROLE', 'NOTIFICATION'))
);

ALTER TABLE IF EXISTS public.constants OWNER TO "postgres";

-- Accounts: Keep old constraints
CREATE TABLE public.accounts (
    account_id UUID NOT NULL PRIMARY KEY,
    account_status BOOLEAN NOT NULL DEFAULT TRUE,
    address CHARACTER VARYING(1000) NOT NULL,
    avatar CHARACTER VARYING(1000),
    email CHARACTER VARYING(1000) NOT NULL,
    password CHARACTER VARYING(1000) NOT NULL,
    phone_number CHARACTER VARYING(1000) NOT NULL,
    refresh_token CHARACTER VARYING(1000),
    system_role UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (system_role) REFERENCES public.constants (constant_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.accounts OWNER TO "postgres";

-- Companies: Keep old constraints
CREATE TABLE public.companies (
    account_id UUID NOT NULL PRIMARY KEY,
    company_name CHARACTER VARYING(1000) NOT NULL,
    company_url CHARACTER VARYING(1000) NOT NULL,
    established_date TIMESTAMP WITH TIME ZONE NOT NULL,
    description TEXT,
    others JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.companies OWNER TO "postgres";

-- Users: Keep old constraints
CREATE TABLE public.users (
    account_id UUID NOT NULL PRIMARY KEY,
    date_of_birth TIMESTAMP WITH TIME ZONE NOT NULL,
    first_name CHARACTER VARYING(1000) NOT NULL,
    gender BOOLEAN NOT NULL,
    last_name CHARACTER VARYING(1000) NOT NULL,
    others JSONB,
    social_media_link CHARACTER VARYING(1000)[],
    resume_link CHARACTER VARYING(1000),
    summary_introduction TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.users OWNER TO "postgres";

-- Application Positions: Replace apply_position, salary_range with text fields; rename note
CREATE TABLE public.application_positions (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    apply_position_title CHARACTER VARYING(1000) NOT NULL, -- New field
    salary CHARACTER VARYING(1000) NOT NULL, -- New field
    status BOOLEAN NOT NULL DEFAULT TRUE,
    description CHARACTER VARYING(10000), -- Renamed from note
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.application_positions OWNER TO "postgres";

-- Application Skills: Replace skill_id with skill_name; rename note
CREATE TABLE public.application_skills (
    id UUID NOT NULL PRIMARY KEY,
    application_position_id UUID NOT NULL,
    skill_name CHARACTER VARYING(1000) NOT NULL, -- New field
    description CHARACTER VARYING(10000), -- Renamed from note
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (application_position_id) REFERENCES public.application_positions (id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.application_skills OWNER TO "postgres";

-- User Educations: Rename note to description; keep old constraints
CREATE TABLE public.user_educations (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    cpa NUMERIC(100,10) NOT NULL,
    majority CHARACTER VARYING(1000),
    description CHARACTER VARYING(1000), -- Renamed from note
    study_end_time TIMESTAMP WITH TIME ZONE,
    study_place CHARACTER VARYING(1000) NOT NULL,
    study_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    is_university BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.users (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.user_educations OWNER TO "postgres";

-- User Experiences: Replace experience_type with experience_title; rename note
CREATE TABLE public.user_experiences (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    experience_end_time TIMESTAMP WITH TIME ZONE,
    experience_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    experience_title CHARACTER VARYING(1000) NOT NULL, -- New field
    description CHARACTER VARYING(1000), -- Renamed from note
    position CHARACTER VARYING(1000) NOT NULL,
    work_place CHARACTER VARYING(1000) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.users (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.user_experiences OWNER TO "postgres";

-- User Awards: Rename note to description; keep old constraints
CREATE TABLE public.user_awards (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    certificate_name CHARACTER VARYING(1000) NOT NULL,
    certificate_time TIMESTAMP WITH TIME ZONE NOT NULL,
    description CHARACTER VARYING(1000), -- Renamed from note
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.users (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.user_awards OWNER TO "postgres";

-- Languages: Replace language_id with language_name; add language_certificate_name
CREATE TABLE public.languages (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    language_name CHARACTER VARYING(1000) NOT NULL, -- New field
    language_score CHARACTER VARYING(1000) NOT NULL,
    language_certificate_name CHARACTER VARYING(1000), -- New field
    language_certificate_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.languages OWNER TO "postgres";

-- Matches: Keep old constraints
CREATE TABLE public.matches (
    id UUID NOT NULL PRIMARY KEY,
    company_id UUID NOT NULL,
    company_matched BOOLEAN,
    matched_time TIMESTAMP WITH TIME ZONE NOT NULL,
    user_id UUID NOT NULL,
    user_matched BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (company_id) REFERENCES public.companies (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES public.users (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.matches OWNER TO "postgres";

-- Conversations: Keep old constraints
CREATE TABLE public.conversations (
    id UUID NOT NULL PRIMARY KEY,
    company_id UUID NOT NULL,
    user_id UUID NOT NULL,
    active_status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (company_id) REFERENCES public.companies (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES public.users (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.conversations OWNER TO "postgres";

-- Messages: Keep old constraints
CREATE TABLE public.messages (
    id UUID NOT NULL PRIMARY KEY,
    account_id UUID NOT NULL,
    content CHARACTER VARYING(10000),
    conversation_id UUID NOT NULL,
    read_status BOOLEAN NOT NULL DEFAULT FALSE,
    url_file CHARACTER VARYING(1000),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (account_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES public.conversations (id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.messages OWNER TO "postgres";

-- Notifications: Keep old constraints
CREATE TABLE public.notifications (
    id UUID NOT NULL PRIMARY KEY,
    content CHARACTER VARYING(1000) NOT NULL,
    notification_type UUID NOT NULL,
    read_status BOOLEAN NOT NULL DEFAULT FALSE,
    object_id UUID NOT NULL,
    receiver_id UUID NOT NULL,
    sender_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (notification_type) REFERENCES public.constants (constant_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES public.accounts (account_id) MATCH FULL ON UPDATE NO ACTION ON DELETE CASCADE
);

ALTER TABLE IF EXISTS public.notifications OWNER TO "postgres";

CREATE TABLE personalization_matrices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_id uuid NOT NULL,
    entity_type varchar NOT NULL, -- 'user', 'job'
    matrix bytea NOT NULL,  -- Lưu dạng binary 
    dimension integer NOT NULL,   -- Kích thước ma trận (NxN)
    metadata jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);
CREATE INDEX idx_personalization_matrices_entity ON personalization_matrices(entity_id, entity_type);
ALTER TABLE IF EXISTS public.personalization_matrices OWNER TO "postgres";
