# Stage 1: Build with Maven
FROM maven:3.8.5-openjdk-17-slim AS build
WORKDIR /app

# Copy only necessary files to leverage Docker cache
COPY pom.xml .
COPY src ./src

# Download dependencies first to leverage cache
RUN mvn dependency:go-offline

# Build the application, skip tests to speed up
RUN mvn clean package -DskipTests

# Stage 2: Create runtime image
FROM openjdk:25-ea-17-jdk-slim
WORKDIR /app

# Install basic tools (if needed) and clean up to reduce image size
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the JAR file from the build stage
COPY --from=build /app/target/job-swipe-0.0.1-SNAPSHOT.jar app.jar

# Create a non-root user for enhanced security
RUN useradd -m appuser && chown appuser:appuser /app
USER appuser

# Expose ports used by the application (HTTP and WebSocket)
EXPOSE 8080 8888

# Define a flexible entrypoint, allowing profile override from docker-compose.yml
ENTRYPOINT ["java", "-jar", "app.jar"]