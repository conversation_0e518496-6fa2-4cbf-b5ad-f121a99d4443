// SHARED BETWEEN PROJECTS - DO NOT MODIFY BY HAND

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'dart:core';
import '/app_common_data/themes/app_theme_data.dart';
import '/app_common_data/common_data/global_key_variable.dart';
import '/shared_customization/extensions/build_context.ext.dart';
import '/generated/translations.g.dart';
import '/shared_customization/animations/custom_loading_animation.dart';
import '/shared_customization/helpers/banner_helper.dart';
import '/shared_customization/helpers/dialogs/dialog_helper.dart';
import '/shared_customization/extensions/string_ext.dart';
import '/shared_customization/helpers/common_helper.dart';
import '/shared_customization/widgets/custom_layout.dart';
import '/shared_customization/widgets/custom_widgets/custom_dismiss_keyboard.dart';
import '/models/file_content/file_content.dart';

class CustomVideoViewScreen extends StatefulWidget {
  final FileContent file;
  const CustomVideoViewScreen({Key? key, required this.file}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _CustomVideoViewScreenState();
  }
}

class _CustomVideoViewScreenState extends State<CustomVideoViewScreen> {
  late VideoPlayerController _videoPlayerController;
  late AppThemeData theme;
  bool _isLoading = true;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    theme =
        GlobalKeyVariable.navigatorState.currentContext!.appTheme.appThemeData;
    initializePlayer();
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    super.dispose();
  }

  Future<void> initializePlayer() async {
    _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget
            .file.url.isNotEmptyOrNull
        ? widget.file.url!
        : "https://assets.mixkit.co/videos/preview/mixkit-daytime-city-traffic-aerial-view-56-large.mp4"));

    await _videoPlayerController.initialize();
    _videoPlayerController.addListener(_videoPlayerListener);
    _videoPlayerController.play();
    setState(() {
      _isLoading = false;
      _isPlaying = true;
    });
  }

  void _videoPlayerListener() {
    if (_videoPlayerController.value.isPlaying != _isPlaying) {
      setState(() {
        _isPlaying = _videoPlayerController.value.isPlaying;
      });
    }
  }

  Future<void> toggleVideo() async {
    if (_videoPlayerController.value.isPlaying) {
      await _videoPlayerController.pause();
    } else {
      await _videoPlayerController.play();
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return [
      if (duration.inHours > 0) hours,
      minutes,
      seconds,
    ].join(':');
  }

  @override
  Widget build(BuildContext context) {
    theme = context.appTheme.appThemeData;
    return CustomDismissKeyboard(
      canPop: true,
      child: Builder(builder: (context) {
        return CustomLayout(
          onWillPop: () => Future.value(true),
          appBarColor: theme.bg_primary,
          title: tr(LocaleKeys.CommonData_Video),
          elevation: 5,
          backgroundColor: theme.bg_primary,
          actions: [
            GestureDetector(
              onTap: () async {
                showConfirmDialog(
                  context,
                  title: tr(LocaleKeys.CommonAction_Download),
                  content: tr(LocaleKeys.CommonNotiAction_DownloadFile),
                  onAccept: () {
                    CommonHelper.downloadFile(
                            widget.file.url!, widget.file.name!)
                        .then((value) {
                      showSuccessBanner(
                          content: tr(
                              LocaleKeys
                                  .CommonNotiAction_DownloadFileSuccessfully,
                              namedArgs: {"filename": widget.file.name!}));
                    });
                  },
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Icon(
                  Icons.file_download_rounded,
                  size: 24,
                  color: theme.icon_primary,
                ),
              ),
            ),
          ],
          body: _isLoading
              ? const Center(child: CustomLoadingAnimation())
              : Column(
                  children: [
                    Expanded(
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: _videoPlayerController.value.aspectRatio,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              VideoPlayer(_videoPlayerController),
                              // Play/pause button
                              GestureDetector(
                                onTap: toggleVideo,
                                child: Container(
                                  color: Colors.transparent,
                                  child: Center(
                                    child: Icon(
                                      _isPlaying
                                          ? Icons.pause_circle_outline
                                          : Icons.play_circle_outline,
                                      size: 60,
                                      color: Colors.white.withOpacity(0.7),
                                    ),
                                  ),
                                ),
                              ),
                              // Video progress indicator
                              Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: Container(
                                  color: Colors.black.withOpacity(0.5),
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                  child: Row(
                                    children: [
                                      ValueListenableBuilder(
                                        valueListenable: _videoPlayerController,
                                        builder: (context, VideoPlayerValue value, child) {
                                          return Text(
                                            _formatDuration(value.position),
                                            style: const TextStyle(color: Colors.white),
                                          );
                                        },
                                      ),
                                      Expanded(
                                        child: VideoProgressIndicator(
                                          _videoPlayerController,
                                          allowScrubbing: true,
                                          padding: const EdgeInsets.symmetric(horizontal: 10),
                                          colors: VideoProgressColors(
                                            playedColor: theme.primary_color,
                                            bufferedColor: theme.white.withOpacity(0.5),
                                            backgroundColor: theme.bg_primary,
                                          ),
                                        ),
                                      ),
                                      ValueListenableBuilder(
                                        valueListenable: _videoPlayerController,
                                        builder: (context, VideoPlayerValue value, child) {
                                          return Text(
                                            _formatDuration(value.duration),
                                            style: const TextStyle(color: Colors.white),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        widget.file.name ?? tr(LocaleKeys.CommonData_Video),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
        );
      }),
    );
  }
}
