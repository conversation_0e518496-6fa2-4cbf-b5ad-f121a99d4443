import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pbl5/models/user_embedding_data/user_embedding_models.dart';
import '/app_common_data/common_data/common_data.dart';
import '/app_common_data/themes/app_theme_data_version.dart';
import '/shared_customization/extensions/string_ext.dart';

class CustomSharedPreferences {
  late final SharedPreferences prefs;

  String? get accessToken => prefs.getString(AppStrings.ACCESS_TOKEN);
  String? get refreshToken => prefs.getString(AppStrings.REFRESH_TOKEN);
  bool get loggedBefore => prefs.getBool(AppStrings.LOGGED_BEFORE) ?? false;
  set loggedBefore(bool value) =>
      prefs.setBool(AppStrings.LOGGED_BEFORE, value);
  bool get keepLogin => prefs.getBool(AppStrings.KEEP_LOGIN) ?? false;

  Future<void> init() async {
    prefs = await SharedPreferences.getInstance();
  }

  AppThemeDataVersion loadThemeVersion() {
    String? themeVersion = prefs.getString(AppStrings.THEME);
    debugPrint("THEME VERSION: $themeVersion".debug);
    if (themeVersion.isNotEmptyOrNull) {
      return AppThemeDataVersion.values.firstWhere(
          (element) => element.label == themeVersion,
          orElse: () => AppThemeDataVersion.v1);
    }
    return AppThemeDataVersion.v1;
  }

  Future<void> setToken(String accessToken, String refreshToken) async {
    await prefs.setString(AppStrings.ACCESS_TOKEN, accessToken);
    await prefs.setString(AppStrings.REFRESH_TOKEN, refreshToken);
    debugPrint("STORAGE: $accessToken -------- $refreshToken".debug);
  }

  bool isEmtpty() {
    return (prefs.getString(AppStrings.ACCESS_TOKEN).isEmptyOrNull ||
        prefs.getString(AppStrings.REFRESH_TOKEN).isEmptyOrNull);
  }

  Future clear() async {
    await prefs.remove(AppStrings.ACCESS_TOKEN);
    await prefs.remove(AppStrings.REFRESH_TOKEN);
  }

  // User Embedding Data Storage Methods
  static const String _userDataSnapshotKey = 'user_data_snapshot';
  static const String _lastEmbeddingUpdateKey = 'last_embedding_update';

  /// Save user data snapshot for comparison
  Future<void> saveUserDataSnapshot(UserDataSnapshot snapshot) async {
    final jsonString = jsonEncode(snapshot.toJson());
    await prefs.setString(_userDataSnapshotKey, jsonString);
    debugPrint('Storage: Saved user data snapshot');
  }

  /// Get user data snapshot for comparison
  UserDataSnapshot? getUserDataSnapshot() {
    final jsonString = prefs.getString(_userDataSnapshotKey);
    if (jsonString != null) {
      try {
        final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
        return UserDataSnapshot.fromJson(jsonMap);
      } catch (e) {
        debugPrint('Storage: Error parsing user data snapshot: $e');
        return null;
      }
    }
    return null;
  }

  /// Save last embedding update timestamp
  Future<void> saveLastEmbeddingUpdate(DateTime timestamp) async {
    await prefs.setString(_lastEmbeddingUpdateKey, timestamp.toIso8601String());
    debugPrint(
        'Storage: Saved last embedding update: ${timestamp.toIso8601String()}');
  }

  /// Get last embedding update timestamp
  DateTime? getLastEmbeddingUpdate() {
    final timestampString = prefs.getString(_lastEmbeddingUpdateKey);
    if (timestampString != null) {
      try {
        return DateTime.parse(timestampString);
      } catch (e) {
        debugPrint(
            'Storage: Error parsing last embedding update timestamp: $e');
        return null;
      }
    }
    return null;
  }

  /// Clear all user embedding data
  Future<void> clearUserEmbeddingData() async {
    await prefs.remove(_userDataSnapshotKey);
    await prefs.remove(_lastEmbeddingUpdateKey);
    debugPrint('Storage: Cleared all user embedding data');
  }
}
