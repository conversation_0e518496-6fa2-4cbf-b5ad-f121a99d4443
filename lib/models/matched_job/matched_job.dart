import 'package:freezed_annotation/freezed_annotation.dart';

part 'matched_job.freezed.dart';
part 'matched_job.g.dart';

@freezed
class MatchedJob with _$MatchedJob {
  const factory MatchedJob({
    @<PERSON>son<PERSON>ey(name: 'job_id') String? jobId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'position_id') String? positionId,
    @Json<PERSON>ey(name: 'similarity_rank') int? similarityRank,
  }) = _MatchedJob;

  static MatchedJob get empty => const MatchedJob();

  factory MatchedJob.fromJson(Map<String, dynamic> json) =>
      _$MatchedJobFromJson(json);
}
