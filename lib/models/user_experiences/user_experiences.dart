import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_experiences.g.dart';
part 'user_experiences.freezed.dart';

@freezed
class UserExperiences with _$UserExperiences {
  @JsonSerializable(includeIfNull: false)
  const factory UserExperiences({
    @J<PERSON><PERSON><PERSON>(name: 'id', includeIfNull: false) String? id,
    @J<PERSON><PERSON><PERSON>(name: 'account_id', includeIfNull: false) String? accountId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'experience_end_time', includeIfNull: false)
    String? experienceEndTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'experience_start_time', includeIfNull: false)
    String? experienceStartTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'experience_type', includeIfNull: false)
    Map<String, dynamic>? experienceType,
    @J<PERSON><PERSON><PERSON>(name: 'experience_title', includeIfNull: false)
    String? experienceTitle,
    @Json<PERSON>ey(name: 'work_place', includeIfNull: false) String? workPlace,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'position', includeIfNull: false) String? position,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'description', includeIfNull: false) String? note,
    @J<PERSON><PERSON><PERSON>(name: 'created_at', includeIfNull: false) String? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at', includeIfNull: false) String? updatedAt,
  }) = _UserExperiences;

  factory UserExperiences.fromJson(Map<String, dynamic> json) =>
      _$UserExperiencesFromJson(json);
}
