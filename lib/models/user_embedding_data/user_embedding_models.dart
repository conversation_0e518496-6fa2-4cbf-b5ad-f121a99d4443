/// Simple user embedding data models without freezed for now
class UserEmbeddingData {
  final String accountId;
  final String description;
  final String name;
  final String structuredText;

  UserEmbeddingData({
    required this.accountId,
    required this.description,
    required this.name,
    required this.structuredText,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'description': description,
      'name': name,
      'structured_text': structuredText,
    };
  }

  factory UserEmbeddingData.fromJson(Map<String, dynamic> json) {
    return UserEmbeddingData(
      accountId: json['account_id'] ?? '',
      description: json['description'] ?? '',
      name: json['name'] ?? '',
      structuredText: json['structured_text'] ?? '',
    );
  }
}

/// Simple user data snapshot for comparison
class UserDataSnapshot {
  final String? firstName;
  final String? lastName;
  final String? summaryIntroduction;
  final String? address;
  final String? phoneNumber;
  final String? email;
  final List<Map<String, dynamic>>? educations;
  final List<Map<String, dynamic>>? experiences;
  final List<Map<String, dynamic>>? languages;
  final List<Map<String, dynamic>>? applicationPositions;
  final List<String>? socialMediaLink;
  final String? lastUpdated;
  final String? dataHash;

  UserDataSnapshot({
    this.firstName,
    this.lastName,
    this.summaryIntroduction,
    this.address,
    this.phoneNumber,
    this.email,
    this.educations,
    this.experiences,
    this.languages,
    this.applicationPositions,
    this.socialMediaLink,
    this.lastUpdated,
    this.dataHash,
  });

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'summaryIntroduction': summaryIntroduction,
      'address': address,
      'phoneNumber': phoneNumber,
      'email': email,
      'educations': educations,
      'experiences': experiences,
      'languages': languages,
      'applicationPositions': applicationPositions,
      'socialMediaLink': socialMediaLink,
      'lastUpdated': lastUpdated,
      'dataHash': dataHash,
    };
  }

  factory UserDataSnapshot.fromJson(Map<String, dynamic> json) {
    return UserDataSnapshot(
      firstName: json['firstName'],
      lastName: json['lastName'],
      summaryIntroduction: json['summaryIntroduction'],
      address: json['address'],
      phoneNumber: json['phoneNumber'],
      email: json['email'],
      educations: json['educations']?.cast<Map<String, dynamic>>(),
      experiences: json['experiences']?.cast<Map<String, dynamic>>(),
      languages: json['languages']?.cast<Map<String, dynamic>>(),
      applicationPositions: json['applicationPositions']?.cast<Map<String, dynamic>>(),
      socialMediaLink: json['socialMediaLink']?.cast<String>(),
      lastUpdated: json['lastUpdated'],
      dataHash: json['dataHash'],
    );
  }
}
