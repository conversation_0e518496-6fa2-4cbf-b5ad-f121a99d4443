import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pbl5/models/application_position/application_position.dart';
import 'package:pbl5/models/matched_job/matched_job.dart';
import 'package:pbl5/models/other_description/other_description.dart';
import 'package:pbl5/models/system_roles_response/system_roles_response.dart';

part 'company.g.dart';

part 'company.freezed.dart';

@freezed
class Company with _$Company {
  const factory Company({
    @JsonKey(name: 'account_id') String? id,
    String? email,
    @Json<PERSON>ey(name: 'account_status') bool? accountStatus,
    String? address,
    String? avatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
    @Json<PERSON>ey(name: "system_role") SystemConstant? systemRole,
    @JsonKey(name: "company_name") String? companyName,
    @JsonKey(name: "company_url") String? companyUrl,
    @JsonKey(name: "established_date") String? establishedDate,
    @Default([])
    @<PERSON>sonKey(name: 'application_positions')
    List<ApplicationPosition> applicationPositions,
    @Default([]) List<OtherDescription> others,
    @Json<PERSON>ey(name: 'created_at') String? createdAt,
    @JsonKey(name: 'updated_at') String? updatedAt,
    @JsonKey(name: "deleted_at") String? deletedAt,
    // New fields for recommendation
    double? similarity,
    @JsonKey(name: 'matched_job') MatchedJob? matchedJob,
  }) = _Company;

  static Company get empty => const Company();

  factory Company.fromJson(Map<String, dynamic> json) =>
      _$CompanyFromJson(json);
}
