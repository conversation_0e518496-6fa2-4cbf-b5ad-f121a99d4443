import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_educations.g.dart';
part 'user_educations.freezed.dart';

@freezed
class UserEducations with _$UserEducations {
  @JsonSerializable(includeIfNull: false)
  const factory UserEducations({
    @J<PERSON><PERSON><PERSON>(name: 'id', includeIfNull: false) String? id,
    @Json<PERSON><PERSON>(name: 'account_id', includeIfNull: false) String? accountId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'study_place', includeIfNull: false) String? studyPlace,
    @Json<PERSON>ey(name: 'study_end_time', includeIfNull: false) String? studyEndTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'study_start_time', includeIfNull: false)
    String? studyStartTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'majority', includeIfNull: false) String? majority,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'cpa', includeIfNull: false) double? cpa,
    @Json<PERSON>ey(name: 'description', includeIfNull: false) String? note,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_university', includeIfNull: false) bool? isUniversity,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at', includeIfNull: false) String? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at', includeIfNull: false) String? updatedAt,
  }) = _UserEducations;

  factory UserEducations.fromJson(Map<String, dynamic> json) =>
      _$UserEducationsFromJson(json);
}
