import 'package:freezed_annotation/freezed_annotation.dart';

part 'credential.g.dart';

part 'credential.freezed.dart';

@freezed
class Credential with _$Credential {
  const factory Credential({
    @<PERSON><PERSON><PERSON><PERSON>(name: "access_token") String? accessToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: "refresh_token") String? refreshToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'expired_at') String? expiredAt,
    @Json<PERSON><PERSON>(name: 'type') String? type,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'firebase_token') String? firebaseToken,
  }) = _Credential;

  static Credential get empty => const Credential();

  factory Credential.fromJson(Map<String, dynamic> json) =>
      _$CredentialFromJson(json);
}
