import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pbl5/models/skill/skill.dart';

part 'application_position.freezed.dart';
part 'application_position.g.dart';

@freezed
class ApplicationPosition with _$ApplicationPosition {
  const factory ApplicationPosition({
    String? id,
    @Json<PERSON>ey(name: 'account_id') String? accountId,
    @Json<PERSON>ey(name: 'apply_position_title') String? applyPositionTitle,
    String? salary,
    bool? status,
    String? note,
    String? description,
    List<Skill>? skills,
    @JsonKey(name: 'created_at') String? createdAt,
    @Json<PERSON>ey(name: 'updated_at') String? updatedAt,
  }) = _ApplicationPosition;

  static ApplicationPosition get empty => const ApplicationPosition();

  factory ApplicationPosition.fromJson(Map<String, dynamic> json) =>
      _$ApplicationPositionFromJson(json);
}
