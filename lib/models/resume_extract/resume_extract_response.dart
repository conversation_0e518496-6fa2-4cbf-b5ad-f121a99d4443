import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pbl5/models/language/language.dart';
import 'package:pbl5/models/skill/skill.dart';
import 'package:pbl5/models/user/user.dart';
import 'package:pbl5/models/user_awards/user_awards.dart';
import 'package:pbl5/models/user_educations/user_educations.dart';
import 'package:pbl5/models/user_experiences/user_experiences.dart';

part 'resume_extract_response.g.dart';
part 'resume_extract_response.freezed.dart';

@freezed
class ResumeExtractResponse with _$ResumeExtractResponse {
  @JsonSerializable(explicitToJson: true)
  const factory ResumeExtractResponse({
    User? userProfile,
    ExtractedData? extractedData,
  }) = _ResumeExtractResponse;

  factory ResumeExtractResponse.fromJson(Map<String, dynamic> json) =>
      _$ResumeExtractResponseFromJson(json);
}

@freezed
class ExtractedData with _$ExtractedData {
  @JsonSerializable(explicitToJson: true)
  const factory ExtractedData({
    BasicInfo? basic_info,
    List<UserEducations>? education,
    List<UserExperiences>? experiences,
    List<UserAwards>? awards,
    List<Language>? languages,
    List<Skill>? skills,
    List<String>? others,
  }) = _ExtractedData;

  factory ExtractedData.fromJson(Map<String, dynamic> json) =>
      _$ExtractedDataFromJson(json);
}

@freezed
class BasicInfo with _$BasicInfo {
  @JsonSerializable(explicitToJson: true)
  const factory BasicInfo({
    String? first_name,
    String? last_name,
    String? email,
    String? phone_number,
    String? address,
    String? date_of_birth,
    bool? gender,
    String? summary_introduction,
    List<String>? social_media_links,
  }) = _BasicInfo;

  factory BasicInfo.fromJson(Map<String, dynamic> json) =>
      _$BasicInfoFromJson(json);
}
