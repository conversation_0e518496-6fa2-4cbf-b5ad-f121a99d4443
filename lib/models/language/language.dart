import 'package:freezed_annotation/freezed_annotation.dart';

part 'language.freezed.dart';
part 'language.g.dart';

@freezed
class Language with _$Language {
  factory Language({
    String? id,
    @Json<PERSON>ey(name: 'account_id') String? accountId,
    @<PERSON>son<PERSON>ey(name: 'language_name') String? languageName,
    @Json<PERSON>ey(name: 'language_score') String? languageScore,
    @Json<PERSON>ey(name: 'language_certificate_name') String? languageCertificateName,
    @Json<PERSON>ey(name: 'language_certificate_date') String? languageCertificateDate,
    @Json<PERSON>ey(name: 'created_at') String? createdAt,
    @Json<PERSON>ey(name: 'updated_at') String? updatedAt,
  }) = _Language;

  factory Language.fromJson(Map<String, dynamic> json) =>
      _$LanguageFromJson(json);
}
