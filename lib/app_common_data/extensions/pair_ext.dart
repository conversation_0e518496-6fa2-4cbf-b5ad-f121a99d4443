import 'package:pbl5/models/pair/pair.dart';

extension PairExt on Pair? {
  bool get isFullyMatched =>
      this?.userMatched == true && this?.companyMatched == true;

  bool get isShowAcceptBtn =>
      this?.userMatched == null && this?.companyMatched == true;

  bool get isShowMakePairAgainBtn =>
      this?.userMatched == false && this?.companyMatched == true;

  bool get isRequestToCompany =>
      this?.userMatched == true && this?.companyMatched == null;

  bool get isCanceledByCompany =>
      this?.userMatched == true && this?.companyMatched == false;
}
