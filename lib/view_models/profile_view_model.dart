import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pbl5/app_common_data/enums/system_constant_prefix.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/models/app_data.dart';
import 'package:pbl5/models/application_position/application_position.dart';
import 'package:pbl5/models/language/language.dart';
import 'package:pbl5/models/skill/skill.dart';
import 'package:pbl5/models/system_roles_response/system_roles_response.dart';
import 'package:pbl5/models/user/user.dart';
import 'package:pbl5/models/user_awards/user_awards.dart';
import 'package:pbl5/models/user_educations/user_educations.dart';
import 'package:pbl5/models/user_experiences/user_experiences.dart';
import 'package:pbl5/services/service_repositories/apply_position_repository.dart';
import 'package:pbl5/services/service_repositories/authentication_repository.dart';
import 'package:pbl5/services/service_repositories/language_repository.dart';
import 'package:pbl5/services/service_repositories/system_constant_repository.dart';
import 'package:pbl5/services/service_repositories/user_repository.dart';
import 'package:pbl5/shared_customization/extensions/date_time_ext.dart';
import 'package:pbl5/shared_customization/extensions/list_ext.dart';
import 'package:pbl5/shared_customization/extensions/string_ext.dart';
import 'package:pbl5/shared_customization/helpers/dialogs/dialog_helper.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/dio_parse_error.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/storages.dart';
import 'package:pbl5/services/user_embedding_service.dart';
import 'package:pbl5/view_models/base_view_model.dart';
import 'package:uuid/uuid.dart';

class ProfileViewModel extends BaseViewModel {
  final AuthenticationRepositoty authRepositoty;
  final UserRepository userRepository;
  final LanguageRepository languageRepository;
  final ApplyPositionRepository applyPositionRepository;
  final SystemConstantRepository systemConstantRepository;
  final CustomSharedPreferences customSharedPreferences;
  final UserEmbeddingService _userEmbeddingService = UserEmbeddingService();
  User? user;
  final emailController = TextEditingController()..text = '';
  final passwordController = TextEditingController()..text = '';
  final addressController = TextEditingController()..text = '';
  final phoneController = TextEditingController()..text = '';
  final firstNameController = TextEditingController()..text = '';
  final lastNameController = TextEditingController()..text = '';
  final dobController = TextEditingController()..text = '';
  final summaryIntroductionController = TextEditingController()
    ..text = 'I am a developer';
  bool gender = true;
  List<String> socialMediaLinks = [];
  File? avatar;

  //for education
  final List<TextEditingController> eduIdControllers = [];
  final List<TextEditingController> studyPlaceControllers = [];
  final List<TextEditingController> studyStartTimeControllers = [];
  final List<TextEditingController> studyEndTimeControllers = [];
  final List<TextEditingController> majorityControllers = [];
  final List<TextEditingController> cpaControllers = [];
  final List<TextEditingController> eduNoteControllers = [];

  //for add education
  final addStudyPlaceController = TextEditingController();
  final addStudyStartTimeController = TextEditingController();
  final addStudyEndTimeController = TextEditingController();
  final addMajorityController = TextEditingController();
  final addCpaController = TextEditingController();
  final addEduNoteController = TextEditingController();

  //for experience
  List<SystemConstant> selectedSystemConstants = [];
  final List<TextEditingController> expIdControllers = [];
  final List<TextEditingController> workPlaceControllers = [];
  final List<TextEditingController> positionControllers = [];
  final List<TextEditingController> expStartTimeControllers = [];
  final List<TextEditingController> expEndTimeControllers = [];
  final List<TextEditingController> expNoteControllers = [];
  final List<TextEditingController> experienceTitleControllers = [];

  //for add experience
  SystemConstant? addSelectedSystemConstants;
  final addWorkPlaceController = TextEditingController();
  final addPositionController = TextEditingController();
  final addExpStartTimeController = TextEditingController();
  final addExpEndTimeController = TextEditingController();
  final addExpNoteController = TextEditingController();
  final addExperienceTitleController = TextEditingController();

  //for award
  final List<TextEditingController> awardIdControllers = [];
  final List<TextEditingController> awardNameControllers = [];
  final List<TextEditingController> awardTimeControllers = [];
  final List<TextEditingController> awardNoteControllers = [];

  //for add award
  final addAwardNameController = TextEditingController();
  final addAwardTimeController = TextEditingController();
  final addAwardNoteController = TextEditingController();

  //for update language
  List<SystemConstant> updateLanguageSystemConstant = [];
  final List<TextEditingController> languageIdControllers = [];
  final List<TextEditingController> updateLanguageScoreControllers = [];
  final List<TextEditingController> updateCertifiedDateControllers = [];

  //for add language
  SystemConstant? addSelectedLanguageSystemConstant;
  final addLanguageScoreController = TextEditingController();
  final addCertifiedDateController = TextEditingController();

  // Thêm các phương thức cho TextEditingController mới
  // Code này nên thêm vào đầu lớp để khai báo các controller mới cho text field
  final addPositionTitleController = TextEditingController();
  final addSalaryController = TextEditingController();
  final addSkillNameController = TextEditingController();

  ProfileViewModel({
    required this.authRepositoty,
    required this.userRepository,
    required this.customSharedPreferences,
    required this.systemConstantRepository,
    required this.languageRepository,
    required this.applyPositionRepository,
  });

  Future<void> updateBasicInfo({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      debugPrint(User(
        id: user?.id,
        email: emailController.text,
        firstName: firstNameController.text,
        lastName: lastNameController.text,
        phoneNumber: phoneController.text,
        address: addressController.text,
        dob: dobController.text.toDatetimeApi,
        gender: gender,
        accountStatus: user?.accountStatus,
        summaryIntroduction: summaryIntroductionController.text,
        socialMediaLink: socialMediaLinks,
      ).toString());
      user = (await userRepository.updateBasicInfo(
        user: User(
          id: user?.id,
          email: emailController.text,
          firstName: firstNameController.text,
          lastName: lastNameController.text,
          phoneNumber: phoneController.text,
          address: addressController.text,
          dob: dobController.text.toDatetimeApi,
          gender: gender,
          accountStatus: user?.accountStatus,
          summaryIntroduction: summaryIntroductionController.text,
          socialMediaLink: socialMediaLinks,
        ),
      ))
          .data;

      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after basic info update
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after basic info update completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after basic info update failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> updateAvatar({
    required File file,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    final cancel = showLoading();
    try {
      await userRepository.updateAvatar(avatar: await file);
      getProfile();
      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    } finally {
      cancel();
    }
  }

  Future<void> uploadResumeForExtraction({
    required File file,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    final cancel = showLoading();
    try {
      final response =
          await userRepository.uploadResumeForExtraction(file: file);
      // Update user data with extracted data
      user = response.data?.userProfile;
      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after CV extraction (force update)
      if (user != null) {
        _userEmbeddingService.forceUpdateEmbedding(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after CV extraction completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after CV extraction failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    } finally {
      cancel();
    }
  }

  Future<void> updateEducation({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== UPDATE EDUCATION CALLED =====');
    try {
      user = (await userRepository.updateEducation(
        userEducations: eduIdControllers
            .map((e) => UserEducations(
                  id: e.text,
                  studyPlace:
                      studyPlaceControllers[eduIdControllers.indexOf(e)].text,
                  studyStartTime:
                      studyStartTimeControllers[eduIdControllers.indexOf(e)]
                          .text
                          .toDatetimeApi,
                  studyEndTime:
                      studyEndTimeControllers[eduIdControllers.indexOf(e)]
                          .text
                          .toDatetimeApi,
                  majority:
                      majorityControllers[eduIdControllers.indexOf(e)].text,
                  cpa: double.tryParse(
                          cpaControllers[eduIdControllers.indexOf(e)].text) ??
                      0,
                  note: eduNoteControllers[eduIdControllers.indexOf(e)].text,
                ))
            .toList(),
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after education update
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after education update completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after education update failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  //update award
  Future<void> updateAward({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    var cancel = showLoading();
    try {
      user = (await userRepository.updateAward(
        userAwards: awardIdControllers
            .map((e) => UserAwards(
                  id: e.text,
                  certificateName:
                      awardNameControllers[awardIdControllers.indexOf(e)].text,
                  certificateTime:
                      awardTimeControllers[awardIdControllers.indexOf(e)]
                          .text
                          .toDatetimeApi,
                  note:
                      awardNoteControllers[awardIdControllers.indexOf(e)].text,
                ))
            .toList(),
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after award update
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after award update completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after award update failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    } finally {
      cancel();
    }
  }

  // Add a method to get the first experience type constant ID, or use a default value
  String getExperienceTypeConstantId() {
    List<SystemConstant> experienceTypes = getIt
            .get<AppData>()
            .systemConstants[SystemConstantPrefix.EXPERIENCE_TYPE] ??
        [];

    if (experienceTypes.isNotEmpty &&
        experienceTypes.first.constantId != null) {
      return experienceTypes.first.constantId!;
    }

    // Fallback to a default UUID if no constants available
    return '00000000-0000-0000-0000-000000000000';
  }

  //update experience
  Future<void> updateExperience({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      String experienceTypeId = getExperienceTypeConstantId();
      user = (await userRepository.updateExperience(
        userExperiences: expIdControllers
            .map((e) => UserExperiences(
                  id: e.text,
                  workPlace:
                      workPlaceControllers[expIdControllers.indexOf(e)].text,
                  position:
                      positionControllers[expIdControllers.indexOf(e)].text,
                  experienceTitle:
                      experienceTitleControllers[expIdControllers.indexOf(e)]
                          .text,
                  experienceType: {
                    'constant_id': experienceTypeId,
                  },
                  experienceStartTime:
                      expStartTimeControllers[expIdControllers.indexOf(e)]
                          .text
                          .toDatetimeApi,
                  experienceEndTime:
                      expEndTimeControllers[expIdControllers.indexOf(e)]
                          .text
                          .toDatetimeApi,
                  note: expNoteControllers[expIdControllers.indexOf(e)].text,
                ))
            .toList(),
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after experience update
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after experience update completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after experience update failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> updateLanguage({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== UPDATE LANGUAGE CALLED =====');
    try {
      var updatedLanguages = (await languageRepository.updateLanguages(
        languages: languageIdControllers
            .map((e) => Language(
                  id: e.text,
                  languageName: updateLanguageSystemConstant[
                          languageIdControllers.indexOf(e)]
                      .constantName,
                  languageScore: updateLanguageScoreControllers[
                          languageIdControllers.indexOf(e)]
                      .text,
                  languageCertificateDate: updateCertifiedDateControllers[
                          languageIdControllers.indexOf(e)]
                      .text
                      .toDatetimeApi,
                ))
            .toList(),
      ));

      if (user != null) {
        user =
            user!.copyWith(languages: updatedLanguages.data ?? user!.languages);
        getIt.get<AppData>().updateUser(user);
      }
      updateUI();

      // Trigger background embedding update after language update
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after language update completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after language update failed: $error');
          },
        );
      }

      onSuccess?.call();
    } catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> updateApplyPosition({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      if (user?.applicationPositions == null) return;
      var response = (await applyPositionRepository.updateApplyPositions(
          applicationPositions: user!.applicationPositions));
      if (response.data != null) {
        getProfile();
        updateUI();
        onSuccess?.call();
      }
    } catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  // clear add Education controller
  void clearAddEducationController() {
    addStudyPlaceController.clear();
    addStudyStartTimeController.clear();
    addStudyEndTimeController.clear();
    addMajorityController.clear();
    addCpaController.clear();
    addEduNoteController.clear();
  }

  //add education
  Future<void> insertEducation({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== INSERT EDUCATION CALLED =====');
    try {
      user = (await userRepository.insertEducation(
        userEducations: [
          UserEducations(
            studyPlace: addStudyPlaceController.text,
            studyStartTime: addStudyStartTimeController.text.toDatetimeApi,
            studyEndTime: addStudyEndTimeController.text.toDatetimeApi,
            majority: addMajorityController.text,
            cpa: double.tryParse(addCpaController.text) ?? 0,
            note: addEduNoteController.text,
          )
        ],
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();

      // Trigger background embedding update after education insert
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after education insert completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after education insert failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  //clear add award controller
  void clearAddAwardController() {
    addAwardNameController.clear();
    addAwardTimeController.clear();
    addAwardNoteController.clear();
  }

  //add award
  Future<void> insertAward({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      user = (await userRepository.insertAward(
        userAwards: [
          UserAwards(
            certificateName: addAwardNameController.text,
            certificateTime: addAwardTimeController.text.toDatetimeApi,
            note: addAwardNoteController.text,
          )
        ],
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  //clear add experience controller
  void clearAddExperienceController() {
    addWorkPlaceController.clear();
    addPositionController.clear();
    addExpStartTimeController.clear();
    addExpEndTimeController.clear();
    addExpNoteController.clear();
    addExperienceTitleController.clear();
  }

  //insert experience
  Future<void> insertExperience({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      String experienceTypeId = getExperienceTypeConstantId();
      user = (await userRepository.insertExperience(
        userExperiences: [
          UserExperiences(
            workPlace: addWorkPlaceController.text,
            position: addPositionController.text,
            experienceTitle: addExperienceTitleController.text,
            experienceType: {
              'constant_id': experienceTypeId,
            },
            experienceStartTime: addExpStartTimeController.text.toDatetimeApi,
            experienceEndTime: addExpEndTimeController.text.toDatetimeApi,
            note: addExpNoteController.text,
          )
        ],
      ))
          .data;
      getIt.get<AppData>().updateUser(user);
      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  // insert language
  Future<void> insertLanguage({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== INSERT LANGUAGE CALLED =====');
    try {
      var insertedLanguages = (await languageRepository.insertLanguages(
        languages: [
          Language(
            languageName: addSelectedLanguageSystemConstant?.constantName,
            languageScore: addLanguageScoreController.text,
            languageCertificateDate:
                addCertifiedDateController.text.toDatetimeApi,
          )
        ],
      ));

      if (user != null) {
        user = user!
            .copyWith(languages: insertedLanguages.data ?? user!.languages);
        getIt.get<AppData>().updateUser(user);
        updateUI();
      }

      // Trigger background embedding update after language insert
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after language insert completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after language insert failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> insertApplyPosition({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      var response = (await applyPositionRepository
          .insertApplyPositions(applicationPositions: [
        ApplicationPosition(
            applyPositionTitle: addPositionTitleController.text,
            salary: addSalaryController.text,
            skills: [
              Skill(
                  id: Uuid().v4(),
                  skillName: addSkillNameController.text,
                  isGenerated: true)
            ],
            status: true)
      ]));

      if (user != null) {
        user = user!.copyWith(
            applicationPositions: (response.data ?? user!.applicationPositions)
                .insertList(response.data!));
        getIt.get<AppData>().user = user;
        updateUI();
      }

      onSuccess?.call();
    } catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> deleteExperience({
    VoidCallback? onSuccess,
    required int index,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== DELETE EXPERIENCE CALLED =====');
    try {
      await userRepository.deleteExperience(
        ids: [expIdControllers[index].text],
      );
      getProfile();
      updateUI();

      // Trigger background embedding update after experience delete
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after experience delete completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after experience delete failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> deleteEducation({
    VoidCallback? onSuccess,
    required int index,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== DELETE EDUCATION CALLED =====');
    try {
      await userRepository.deleteEducation(
        ids: [eduIdControllers[index].text],
      );
      getProfile();
      updateUI();

      // Trigger background embedding update after education delete
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after education delete completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after education delete failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> deleteLanguage({
    VoidCallback? onSuccess,
    required int index,
    Function(String)? onFailure,
  }) async {
    debugPrint('ProfileViewModel: ===== DELETE LANGUAGE CALLED =====');
    try {
      await languageRepository.deleteLanguage(
        ids: [languageIdControllers[index].text],
      );
      getProfile();
      updateUI();

      // Trigger background embedding update after language delete
      if (user != null) {
        _userEmbeddingService.checkAndUpdateEmbeddingOptimized(
          user!,
          onSuccess: () {
            debugPrint(
                'ProfileViewModel: User embedding update after language delete completed');
          },
          onError: (error) {
            debugPrint(
                'ProfileViewModel: User embedding update after language delete failed: $error');
          },
        );
      }

      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> deleteApplyPosition({
    VoidCallback? onSuccess,
    required String? id,
    Function(String)? onFailure,
  }) async {
    try {
      if (id.isEmptyOrNull) return;
      await applyPositionRepository.deleteApplyPositions(ids: [id!]);
      getProfile();
      updateUI();
      onSuccess?.call();
    } catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> deleteAward({
    VoidCallback? onSuccess,
    required int index,
    Function(String)? onFailure,
  }) async {
    try {
      await userRepository.deleteAward(
        ids: [awardIdControllers[index].text],
      );
      getProfile();
      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }

  Future<void> getProfile({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
    bool isShowLoading = true,
  }) async {
    final cancel = showLoading();
    try {
      user = (await userRepository.getProfile()).data;
      //basic profile
      getIt.get<AppData>().updateUser(user);
      emailController.text = user?.email ?? '';
      firstNameController.text = user?.firstName ?? '';
      lastNameController.text = user?.lastName ?? '';
      phoneController.text = user?.phoneNumber ?? '';
      addressController.text = user?.address ?? '';
      dobController.text = user?.dob.toDateTime.toDayMonthYear() ?? '';
      summaryIntroductionController.text = user?.summaryIntroduction ?? '';
      socialMediaLinks = user?.socialMediaLink ?? [];
      gender = user?.gender ?? true;
      //education
      eduIdControllers.clear();
      studyPlaceControllers.clear();
      studyStartTimeControllers.clear();
      studyEndTimeControllers.clear();
      majorityControllers.clear();
      cpaControllers.clear();
      eduNoteControllers.clear();
      //add education
      addStudyPlaceController.clear();
      addStudyStartTimeController.clear();
      addStudyEndTimeController.clear();
      addMajorityController.clear();
      addCpaController.clear();
      addEduNoteController.clear();

      //experience
      expIdControllers.clear();
      workPlaceControllers.clear();
      positionControllers.clear();
      expStartTimeControllers.clear();
      expEndTimeControllers.clear();
      expNoteControllers.clear();
      experienceTitleControllers.clear();
      selectedSystemConstants.clear();

      //add experience
      addWorkPlaceController.clear();
      addPositionController.clear();
      addExpStartTimeController.clear();
      addExpEndTimeController.clear();
      addExpNoteController.clear();

      //award
      awardIdControllers.clear();
      awardNameControllers.clear();
      awardTimeControllers.clear();
      awardNoteControllers.clear();

      //add award
      addAwardNameController.clear();
      addAwardTimeController.clear();
      addAwardNoteController.clear();

      //update language
      languageIdControllers.clear();
      updateLanguageScoreControllers.clear();
      updateCertifiedDateControllers.clear();

      //add language
      addLanguageScoreController.clear();
      addCertifiedDateController.clear();

      // create Controller for each education
      user?.educations.forEach((education) {
        eduIdControllers.add(TextEditingController(text: education.id));
        studyPlaceControllers
            .add(TextEditingController(text: education.studyPlace));
        studyStartTimeControllers.add(TextEditingController(
            text: education.studyStartTime.toDateTime.toDayMonthYear()));
        studyEndTimeControllers.add(TextEditingController(
            text: education.studyEndTime.toDateTime.toDayMonthYear()));
        majorityControllers
            .add(TextEditingController(text: education.majority));
        cpaControllers
            .add(TextEditingController(text: education.cpa.toString()));
        eduNoteControllers.add(TextEditingController(text: education.note));
      });

      //create Controller for each experience
      user?.experiences.forEach((experience) {
        expIdControllers.add(TextEditingController(text: experience.id));
        workPlaceControllers
            .add(TextEditingController(text: experience.workPlace));
        positionControllers
            .add(TextEditingController(text: experience.position));
        expStartTimeControllers.add(TextEditingController(
            text: experience.experienceStartTime.toDateTime.toDayMonthYear()));
        expEndTimeControllers.add(TextEditingController(
            text: experience.experienceEndTime.toDateTime.toDayMonthYear()));
        expNoteControllers.add(TextEditingController(text: experience.note));
        experienceTitleControllers
            .add(TextEditingController(text: experience.experienceTitle ?? ''));
        selectedSystemConstants.add(
            SystemConstant(constantName: experience.experienceTitle ?? ''));
      });
      //create Controller for each award

      user?.awards.forEach((award) {
        awardIdControllers.add(TextEditingController(text: award.id));
        awardNameControllers
            .add(TextEditingController(text: award.certificateName));
        awardTimeControllers.add(TextEditingController(
            text: award.certificateTime.toDateTime.toDayMonthYear()));
        awardNoteControllers.add(TextEditingController(text: award.note));
      });

      //create Controller for each language
      user?.languages.forEach((language) {
        languageIdControllers.add(TextEditingController(text: language.id));
        updateLanguageSystemConstant
            .add(SystemConstant(constantName: language.languageName ?? ''));
        updateLanguageScoreControllers
            .add(TextEditingController(text: language.languageScore));
        updateCertifiedDateControllers.add(TextEditingController(
            text:
                language.languageCertificateDate?.toDateTime.toDayMonthYear() ??
                    ''));
      });

      onSuccess?.call();
      updateUI();

      // NOTE: Removed automatic embedding update from getProfile() to prevent
      // unnecessary API calls when just fetching user data.
      // Embedding updates should only happen when user actually modifies data.
      //
      // If you need to trigger embedding on profile screen navigation,
      // use checkDataChangesOnly() method instead to avoid API calls when no changes exist.

      // Optional: Check for data changes without triggering API call
      // This is useful for logging/analytics but doesn't trigger embedding update
      if (user != null) {
        _userEmbeddingService.checkDataChangesOnly(
          user!,
          onDataChanged: () {
            debugPrint(
                'ProfileViewModel: Data changes detected during profile fetch (no API call triggered)');
          },
          onNoChanges: () {
            debugPrint(
                'ProfileViewModel: No data changes detected during profile fetch');
          },
        );
      }
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    } finally {
      if (isShowLoading) {
        cancel();
      }
    }
  }

  Future<void> logOut({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    final cancel = showLoading();
    try {
      await authRepositoty.logOut();
      customSharedPreferences.clear();
      getIt.get<AppData>().clear();

      // Clear embedding data on logout
      await _userEmbeddingService.clearEmbeddingData();

      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    } finally {
      cancel();
    }
  }

  /// Debug method to clear embedding debouncing
  void clearEmbeddingDebouncing() {
    _userEmbeddingService.clearDebouncing();
    debugPrint('ProfileViewModel: Cleared embedding debouncing for testing');
  }

  /// Debug method to force embedding update
  void forceEmbeddingUpdate() {
    if (user != null) {
      _userEmbeddingService.forceUpdateEmbedding(
        user!,
        onSuccess: () {
          debugPrint('ProfileViewModel: Force embedding update completed');
        },
        onError: (error) {
          debugPrint('ProfileViewModel: Force embedding update failed: $error');
        },
      );
    }
  }
}
