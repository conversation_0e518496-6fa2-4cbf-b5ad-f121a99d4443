import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pbl5/app_common_data/common_data/global_key_variable.dart';
import 'package:pbl5/models/conversation/conversation.dart';
import 'package:pbl5/models/message/message.dart';
import 'package:pbl5/services/firebase_service.dart';
import 'package:pbl5/services/service_repositories/chat_repository.dart';
import 'package:pbl5/shared_customization/extensions/api_page_response_ext.dart';
import 'package:pbl5/shared_customization/extensions/list_ext.dart';
import 'package:pbl5/shared_customization/extensions/string_ext.dart';
import 'package:pbl5/shared_customization/helpers/dialogs/dialog_helper.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/dio_parse_error.dart';
import 'package:pbl5/view_models/base_view_model.dart';
import 'package:pbl5/services/api_models/api_page_response/api_page_response.dart';

class ChatViewModel extends BaseViewModel {
  ///
  /// State
  ///
  ApiPageResponse<Message>? messages;
  Conversation? conversation;
  bool canLoadMore = true;
  BuildContext get _context => GlobalKeyVariable.navigatorState.currentContext!;

  // Firebase realtime messaging
  StreamSubscription<List<Map<String, dynamic>>>? _messagesSubscription;
  List<Map<String, dynamic>> _firebaseMessages = [];

  ///
  /// Other dependencies
  ///
  final ChatRepository _chatRepository;

  ChatViewModel({required ChatRepository chatRepository})
      : _chatRepository = chatRepository;

  ///
  /// Events
  ///
  ///
  Future<void> getConversation(String id) async {
    try {
      var response = await _chatRepository.getConversationById(id);
      conversation = response.data;

      // Setup Firebase realtime messaging
      await _setupFirebaseMessaging(id);
    } catch (e) {
      debugPrint("Error: $e".debug);
      showErrorDialog(_context, content: parseError(e));
      return;
    }
    updateUI();
  }

  Future<void> _setupFirebaseMessaging(String conversationId) async {
    try {
      // Create or get chat room in Firebase
      if (conversation?.user?.id != null && conversation?.company?.id != null) {
        await FirebaseService.createOrGetChatRoom(
          conversationId,
          [conversation!.user!.id!, conversation!.company!.id!],
        );
      }

      // Listen to Firebase messages
      _messagesSubscription?.cancel();
      debugPrint(
          'Setting up Firebase messages stream for conversation: $conversationId');
      _messagesSubscription =
          FirebaseService.getMessagesStream(conversationId).listen(
        (firebaseMessages) {
          debugPrint('Received ${firebaseMessages.length} Firebase messages');
          _firebaseMessages = firebaseMessages;
          _mergeFirebaseMessages();
          updateUI();
        },
        onError: (error) {
          debugPrint('Firebase messages stream error: $error');
        },
      );
    } catch (e) {
      debugPrint('Error setting up Firebase messaging: $e');
    }
  }

  void _mergeFirebaseMessages() {
    if (_firebaseMessages.isEmpty || messages?.data == null) return;

    // Convert Firebase messages to Message objects and merge with existing messages
    final List<Message> allMessages = List.from(messages!.data!);

    for (final firebaseMsg in _firebaseMessages) {
      final message = Message(
        id: firebaseMsg['message_id'],
        content: firebaseMsg['content'],
        senderId: firebaseMsg['sender_id'],
        conversationId: conversation?.id,
        readStatus: false,
        urlFile: firebaseMsg['file_url'],
        createdAt: firebaseMsg['timestamp'],
      );

      // Add if not already exists
      if (!allMessages.any((m) => m.id == message.id)) {
        allMessages.add(message);
      }
    }

    // Sort by timestamp
    allMessages.sort((a, b) {
      if (a.createdAt == null || b.createdAt == null) return 0;
      return DateTime.parse(b.createdAt!)
          .compareTo(DateTime.parse(a.createdAt!));
    });

    messages = messages?.copyWith(data: allMessages);
  }

  Future<void> getMessages({int page = 1}) async {
    if (conversation == null || conversation!.id.isEmptyOrNull) return;
    try {
      var response =
          await _chatRepository.getMessages(conversation!.id!, page: page);
      messages = messages.insertPage(response);
      canLoadMore = (response.paging?.nextPage ?? 0) >
          (response.paging?.currentPage ?? 0);
    } catch (e) {
      debugPrint("Error: $e".debug);
      messages = ApiPageResponse.empty();
    }
    updateUI();
  }

  Future<void> sendMessage({
    String? content,
    List<File> files = const [],
  }) async {
    if (conversation == null || conversation!.id.isEmptyOrNull) return;
    if (content.isEmptyOrNull && files.isEmptyOrNull) return;
    try {
      // Send via traditional API first
      var response = await _chatRepository.sendMessage(conversation!.id!,
          content: content, files: files);
      if (response.data == null) return;

      // Add to local messages
      for (var m in response.data!) {
        messages = messages.insertFirst(m,
            removeIfDuplicate: (element) => element.id == m.id);
      }

      // Send via Firebase for realtime updates
      if (content != null && content.isNotEmpty) {
        await _sendFirebaseMessage(content);
      }
    } catch (e) {
      debugPrint("Error: $e".debug);
      showErrorDialog(_context, content: parseError(e));
      return;
    }
    updateUI();
  }

  Future<void> _sendFirebaseMessage(String content) async {
    try {
      if (conversation?.id == null) return;

      // Get current user ID from AppData
      final currentUserId = getIt.get<AppData>().user?.id;
      if (currentUserId == null) {
        debugPrint('Error: Current user ID is null');
        return;
      }

      await FirebaseService.sendMessage(
        conversationId: conversation!.id!,
        senderId: currentUserId,
        content: content,
        messageType: 'TEXT',
      );
    } catch (e) {
      debugPrint('Error sending Firebase message: $e');
    }
  }

  Future<void> markAsRead(String messageId) async {
    if (conversation == null || conversation!.id.isEmptyOrNull) return;
    try {
      var isSuccess =
          await _chatRepository.markAsReadMessage(conversation!.id!, messageId);
      if (isSuccess) {
        messages = messages.update(
            (element) => element.copyWith(readStatus: true),
            (element) => element.id == messageId);
        updateUI();
      }
    } catch (e) {
      debugPrint("Error: $e".debug);
      showErrorDialog(_context, content: parseError(e));
    }
  }

  Future<void> markAllAsRead() async {
    if (conversation == null || conversation!.id.isEmptyOrNull) return;
    try {
      var isSuccess =
          await _chatRepository.markAllAsReadMessage(conversation!.id!);
      if (isSuccess) {
        messages = messages.update(
            (element) => element.copyWith(readStatus: true),
            (element) => element.readStatus == false);
        updateUI();
      }
    } catch (e) {
      debugPrint("Error: $e".debug);
      showErrorDialog(_context, content: parseError(e));
    }
  }

  void clearData() async {
    messages = null;
    conversation = null;
    canLoadMore = true;

    // Cancel Firebase subscription
    _messagesSubscription?.cancel();
    _messagesSubscription = null;
    _firebaseMessages.clear();

    updateUI();
  }

  @override
  void dispose() {
    _messagesSubscription?.cancel();
    super.dispose();
  }
}
