import 'package:flutter/material.dart';
import 'package:pbl5/app_common_data/common_data/global_key_variable.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/routes.dart';
import 'package:pbl5/services/api_models/api_page_response/api_page_response.dart';
import 'package:pbl5/services/service_repositories/swipe_selection_repository.dart';
import 'package:pbl5/shared_customization/extensions/api_page_response_ext.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/dio_parse_error.dart';
import 'package:pbl5/shared_customization/widgets/dialogs/match_success_dialog.dart';
import 'package:pbl5/view_models/base_view_model.dart';
import 'package:pbl5/services/swipe_feedback_service.dart';

class SwipeSelectionViewModel extends BaseViewModel {
  final SwipeSelectionRepository recPredictRepo;
  final SwipeFeedbackService? feedbackService;

  ApiPageResponse<Company>? companies;

  SwipeSelectionViewModel({
    required this.recPredictRepo,
    this.feedbackService,
  });

  Future<void> getRecommendedCompanies({
    int page = 1,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      var response = (await recPredictRepo.getRecommendedCompanies(page: page));
      companies = companies.insertPage(response);
      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
      companies = ApiPageResponse.empty();
    }
  }

  Future<void> requestMatchedPair({
    String? companyId,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
    Company? company,
  }) async {
    try {
      final result = await recPredictRepo.requestMatchedPair(companyId!);

      // Log match request for potential Firebase notification
      debugPrint('Match requested with company: $companyId');
      debugPrint('Match result: ${result.data?.toString()}');

      // Check if it's a successful match (both parties liked each other)
      if (result.data != null && company != null) {
        final userMatched = result.data?.userMatched ?? false;
        final companyMatched = result.data?.companyMatched ?? false;

        // If both parties matched, show success dialog
        if (userMatched && companyMatched) {
          // Show match success dialog
          _showMatchSuccessDialog(company);
        }
      }

      updateUI();
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(error.toString());
    }
  }

  void _showMatchSuccessDialog(Company company) {
    final context = GlobalKeyVariable.navigatorState.currentContext;
    if (context == null) return;

    MatchSuccessDialog.show(
      context,
      company: company,
      onViewProfile: () {
        Navigator.of(context).pop(); // Close dialog
        Navigator.of(context)
            .pushNamed(Routes.companyDetail, arguments: company.id);
      },
      onSendMessage: () {
        Navigator.of(context).pop(); // Close dialog
        // Navigate to chat - this would need conversation ID from the match result
        // For now, navigate to messages tab
        Navigator.of(context).pushNamedAndRemoveUntil(
          Routes.main,
          (route) => false,
          arguments: {'initialTab': 2},
        );
      },
      onKeepSwiping: () {
        Navigator.of(context).pop(); // Close dialog
      },
    );
  }

  /// Gửi feedback like cho company
  Future<void> submitLikeFeedback({
    required String companyId,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      if (feedbackService == null) {
        onFailure?.call('Feedback service not available');
        return;
      }

      final result = await feedbackService!.submitLike(
        itemId: companyId,
        itemType: 'company',
      );

      debugPrint('Like feedback submitted: $result');
      onSuccess?.call();
    } catch (error) {
      debugPrint('Error submitting like feedback: $error');
      onFailure?.call(parseError(error));
    }
  }

  /// Gửi feedback dislike cho company
  Future<void> submitDislikeFeedback({
    required String companyId,
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      if (feedbackService == null) {
        onFailure?.call('Feedback service not available');
        return;
      }

      final result = await feedbackService!.submitDislike(
        itemId: companyId,
        itemType: 'company',
      );

      debugPrint('Dislike feedback submitted: $result');
      onSuccess?.call();
    } catch (error) {
      debugPrint('Error submitting dislike feedback: $error');
      onFailure?.call(parseError(error));
    }
  }

  /// Lấy trạng thái feedback
  Future<void> getFeedbackStatus({
    Function(Map<String, dynamic>)? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      if (feedbackService == null) {
        onFailure?.call('Feedback service not available');
        return;
      }

      final result = await feedbackService!.getFeedbackStatus();
      debugPrint('Feedback status: $result');
      onSuccess?.call(result);
    } catch (error) {
      debugPrint('Error getting feedback status: $error');
      onFailure?.call(parseError(error));
    }
  }

  /// Ép buộc xử lý batch feedback
  Future<void> forceBatchProcess({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
  }) async {
    try {
      if (feedbackService == null) {
        onFailure?.call('Feedback service not available');
        return;
      }

      final result = await feedbackService!.forceBatchProcess();
      debugPrint('Batch process forced: $result');
      onSuccess?.call();
    } catch (error) {
      debugPrint('Error forcing batch process: $error');
      onFailure?.call(parseError(error));
    }
  }
}
