import 'package:pbl5/services/service_repositories/notification_repository.dart';
import 'package:pbl5/view_models/base_view_model.dart';

class AppNotificationViewModel extends BaseViewModel {
  ///
  /// State
  ///
  int unreadNotificationCount = 0;

  ///
  /// Dependencies
  ///
  final NotificationRepository _notificationRepository;

  AppNotificationViewModel({
    required NotificationRepository notificationRepository,
  }) : _notificationRepository = notificationRepository;

  ///
  /// Events
  ///
  Future<void> getUnreadNotificationCount() async {
    unreadNotificationCount =
        await _notificationRepository.getUnreadNotificationCount();
    updateUI();
  }

  void clearNotification() {
    unreadNotificationCount = 0;
    updateUI();
  }
}
