import 'package:flutter/material.dart';
import 'package:pbl5/models/system_roles_response/system_roles_response.dart';
import 'package:pbl5/models/user/user.dart';
import 'package:pbl5/services/service_repositories/authentication_repository.dart';
import 'package:pbl5/shared_customization/extensions/string_ext.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/dio_parse_error.dart';
import 'package:pbl5/view_models/base_view_model.dart';

class RegisterViewModel extends BaseViewModel {
  final AuthenticationRepositoty authenticationRepositoty;
  final emailController = TextEditingController()..text = '';
  final passwordController = TextEditingController()..text = '';
  final addressController = TextEditingController()..text = '';
  final phoneController = TextEditingController()..text = '';
  final firstNameController = TextEditingController()..text = '';
  final lastNameController = TextEditingController()..text = '';
  final dobController = TextEditingController()..text = '';
  bool gender = true;

  RegisterViewModel({
    required this.authenticationRepositoty,
  });

  void updateGender(bool value) {
    gender = value;
    updateUI();
  }

  Future<void> onRegisterPressed({
    VoidCallback? onSuccess,
    Function(String)? onFailure,
    required String systemRoleId,
  }) async {
    try {
      debugPrint(emailController.text);
      debugPrint(passwordController.text);
      await authenticationRepositoty.register(
        user: User(
          email: emailController.text,
          password: passwordController.text,
          address: addressController.text,
          phoneNumber: phoneController.text,
          firstName: firstNameController.text,
          lastName: lastNameController.text,
          gender: gender,
          dob: dobController.text.toDatetimeApi,
          systemRole: SystemConstant(constantId: systemRoleId),
        ),
      );
      onSuccess?.call();
    } on Exception catch (error) {
      onFailure?.call(parseError(error));
    }
  }
}
