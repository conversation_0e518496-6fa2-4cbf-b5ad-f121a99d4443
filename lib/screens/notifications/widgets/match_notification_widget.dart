import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/app_common_data/app_text_sytle.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/generated/assets.gen.dart';
import 'package:pbl5/shared_customization/widgets/custom_container.dart';
import 'package:pbl5/shared_customization/widgets/custom_widgets/custom_image.dart';
import 'package:pbl5/shared_customization/widgets/texts/custom_text.dart';

class MatchNotificationWidget extends StatelessWidget {
  const MatchNotificationWidget({
    super.key,
    required this.companyName,
    required this.companyLogo,
    required this.matchId,
    required this.onTap,
    this.timestamp,
  });

  final String companyName;
  final String? companyLogo;
  final String matchId;
  final VoidCallback onTap;
  final DateTime? timestamp;

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      onPressed: onTap,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      borderRadius: BorderRadius.circular(12.r),
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: Row(
        children: [
          // Match icon
          CustomContainer(
            width: 50.w,
            height: 50.w,
            borderRadius: BorderRadius.circular(25.r),
            color: kPrimaryColor.withOpacity(0.1),
            child: Icon(
              Icons.favorite,
              color: kPrimaryColor,
              size: 24.w,
            ),
          ),

          SizedBox(width: 12.w),

          // Company logo
          CustomContainer(
            width: 40.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.r),
            clipBehavior: Clip.hardEdge,
            child: CustomImage(
              url: companyLogo,
              width: 40.w,
              height: 40.w,
              fit: BoxFit.cover,
            ),
          ),

          SizedBox(width: 12.w),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  'New Match! 🎉',
                  style: AppTextStyle.titleText,
                  color: kPrimaryColor,
                  size: 16.sp,
                ),
                SizedBox(height: 4.h),
                CustomText(
                  'You matched with $companyName',
                  style: AppTextStyle.bodyText,
                  color: Colors.black87,
                  size: 14.sp,
                ),
                if (timestamp != null) ...[
                  SizedBox(height: 4.h),
                  CustomText(
                    _formatTimestamp(timestamp!),
                    style: AppTextStyle.description,
                    color: Colors.grey,
                    size: 12.sp,
                  ),
                ],
              ],
            ),
          ),

          // Arrow icon
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey,
            size: 16.w,
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
