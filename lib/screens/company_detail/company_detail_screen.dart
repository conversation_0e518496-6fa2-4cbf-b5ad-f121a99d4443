import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/screens/base/base_view.dart';
import 'package:pbl5/screens/company_detail/tabs/jobs_tab.dart';
import 'package:pbl5/screens/company_detail/tabs/overview_tab.dart';
import 'package:pbl5/shared_customization/extensions/build_context.ext.dart';
import 'package:pbl5/view_models/company_detail_view_model.dart';
import 'package:provider/provider.dart';
import 'package:rive/rive.dart';

class CompanyDetailScreen extends StatefulWidget {
  final String companyId;

  CompanyDetailScreen({super.key, required this.companyId});

  @override
  State<CompanyDetailScreen> createState() => _CompanyDetailScreenState();
}

class _CompanyDetailScreenState extends State<CompanyDetailScreen> {
  bool isShowLoading = false;
  bool isShowConfetti = false;
  late SMITrigger check;
  late SMITrigger error;
  late SMITrigger reset;
  late SMITrigger confetti;
  late final CompanyDetailViewModel viewModel;

  initState() {
    viewModel = getIt.get<CompanyDetailViewModel>();
    viewModel.initState(userId: widget.companyId);
    super.initState();
  }

  StateMachineController getRiveController(Artboard artboard) {
    StateMachineController? controller =
        StateMachineController.fromArtboard(artboard, "State Machine 1");
    artboard.addController(controller!);
    return controller;
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
      viewModel: viewModel,
      backgroundColor: orangePink,
      canPop: true,
      appBar: AppBar(
        backgroundColor: orangePink,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.chevron_left,
            size: 30,
            color: Colors.white,
          ),
          onPressed: () {
            context.pop();
            viewModel.clear();
          },
        ),
      ),
      mobileBuilder: (context) {
        var company = context.select((CompanyDetailViewModel vm) => vm.company);
        var pair = context.select((CompanyDetailViewModel vm) => vm.pair);
        return company == null
            ? Center(child: Container())
            : Column(
                children: [
                  Expanded(
                    flex: 1,
                    child: Container(
                      child: Column(
                        children: [
                          Flexible(
                            child: Padding(
                              padding: EdgeInsets.only(
                                top: 5.h,
                              ), // Add padding to prevent clipping
                              child: Stack(
                                alignment: Alignment.center,
                                clipBehavior: Clip.none,
                                children: [
                                  _buildCompanyImage(company),
                                  // Status indicator positioned at top-right corner
                                  if (company.accountStatus != null)
                                    Positioned(
                                      top: -5,
                                      right: -5,
                                      child: company.accountStatus!
                                          ? Container(
                                              padding: EdgeInsets.all(2),
                                              decoration: BoxDecoration(
                                                color: Colors.lightGreenAccent,
                                                borderRadius:
                                                    BorderRadius.circular(100),
                                              ),
                                              child: Container(
                                                padding: EdgeInsets.all(6),
                                                decoration: BoxDecoration(
                                                  color: Colors.green,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          100),
                                                ),
                                              ),
                                            )
                                          : Container(
                                              padding: EdgeInsets.all(2),
                                              decoration: BoxDecoration(
                                                color: Colors.grey,
                                                borderRadius:
                                                    BorderRadius.circular(100),
                                              ),
                                              child: Container(
                                                padding: EdgeInsets.all(6),
                                                decoration: BoxDecoration(
                                                  color: Colors.black38,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          100),
                                                ),
                                              ),
                                            ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 12.h),
                          Text(
                            company.companyName ?? '',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 19.sp,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.visible,
                          ),
                          Text(
                            'Jobs available',
                            style: TextStyle(
                              color: Colors.white54,
                            ),
                          ),
                          SizedBox(
                            height: 25.h,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Container(
                      color: Colors.white,
                      child: DefaultTabController(
                        length: 2,
                        child: Scaffold(
                          backgroundColor: orangePink,
                          appBar: TabBar(
                            indicatorColor: Colors.white,
                            tabs: [
                              Tab(
                                child: Text(
                                  'OVERVIEW',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Tab(
                                child: Text(
                                  'JOBS',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          body: Container(
                              color: Colors.white,
                              child: TabBarView(
                                children: <Widget>[
                                  OverviewTab(company: company, pair: pair),
                                  JobsTab(
                                      positions: company.applicationPositions,
                                      company: company),
                                ],
                              )),
                        ),
                      ),
                    ),
                  ),
                ],
              );
      },
    );
  }

  Widget _buildCompanyImage(Company company) {
    // Check if avatar URL is valid
    if (company.avatar == null ||
        company.avatar!.isEmpty ||
        company.avatar!.trim().isEmpty ||
        company.avatar == "file:///" ||
        company.avatar!.startsWith("file:///")) {
      return Icon(
        Icons.location_city,
        size: 95,
        color: Colors.white,
      );
    }

    // Additional URL validation
    Uri? uri = Uri.tryParse(company.avatar!);
    if (uri == null ||
        (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https'))) {
      return Icon(
        Icons.location_city,
        size: 95,
        color: Colors.white,
      );
    }

    return Image.network(
      company.avatar!,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
            strokeWidth: 2,
            color: Colors.white,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.location_city,
          size: 95,
          color: Colors.white,
        );
      },
    );
  }
}
