import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/app_common_data/app_text_sytle.dart';
import 'package:pbl5/app_common_data/common_data/global_variable.dart';
import 'package:pbl5/app_common_data/extensions/pair_ext.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/models/pair/pair.dart';
import 'package:pbl5/shared_customization/extensions/date_time_ext.dart';
import 'package:pbl5/shared_customization/extensions/string_ext.dart';
import 'package:pbl5/shared_customization/helpers/banner_helper.dart';
import 'package:pbl5/shared_customization/helpers/dialogs/dialog_helper.dart';
import 'package:pbl5/shared_customization/widgets/buttons/custom_button.dart';
import 'package:pbl5/shared_customization/widgets/texts/custom_text.dart';
import 'package:pbl5/view_models/company_detail_view_model.dart';

class OverviewTab extends StatelessWidget {
  final Company company;
  final Pair? pair;
  const OverviewTab({
    super.key,
    required this.company,
    required this.pair,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            left: 20.w,
            right: 20.w,
            top: 20.h,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text(
                "About Us",
                style: TextStyle(
                  fontSize: 19,
                  color: Colors.black87,
                  fontFamily: 'Poppins',
                ),
              ),
              // Email - only show if has data
              if (company.email != null && company.email!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Row(
                    children: [
                      Icon(Icons.mail),
                      SizedBox(width: 15.h),
                      Expanded(
                        child: Text(
                          company.email!,
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              // Company URL - only show if has data
              if (company.companyUrl != null && company.companyUrl!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Row(
                    children: [
                      Icon(Icons.link),
                      SizedBox(width: 15.h),
                      Expanded(
                        child: Text(
                          company.companyUrl!,
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              // Established Date - only show if has data
              if (company.establishedDate != null)
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Row(
                    children: [
                      Icon(Icons.date_range),
                      SizedBox(width: 15.h),
                      Expanded(
                        child: Text(
                          company.establishedDate!.toDateTime.toDayMonthYear(),
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              // Phone Number - only show if has data
              if (company.phoneNumber != null &&
                  company.phoneNumber!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Row(
                    children: [
                      Icon(Icons.phone),
                      SizedBox(width: 15.h),
                      Expanded(
                        child: Text(
                          company.phoneNumber!,
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              // Address - only show if has data
              if (company.address != null && company.address!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Row(
                    children: [
                      Icon(Icons.location_on_rounded),
                      SizedBox(width: 15.h),
                      Expanded(
                        child: Text(
                          company.address!,
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              if (pair != null) SizedBox(height: 20.h),
              if (pair != null)
                Builder(
                  builder: (context) {
                    if (pair!.isFullyMatched) {
                      return CustomButton(
                        contentAlignment: Alignment.center,
                        onPressed: () {
                          showConfirmDialog(
                            context,
                            title: "Cancel pair",
                            content:
                                "Are you sure you want to cancel this pair?",
                            onAccept: () {
                              getIt.get<CompanyDetailViewModel>().declinePair(
                                  onSuccess: () {
                                showSuccessBanner(
                                    content: "Cancel pair successfully");
                              }, onFailure: (e) {
                                showErrorBanner(content: e);
                              });
                            },
                          );
                        },
                        color: Colors.grey,
                        label: "Cancel pair",
                      );
                    }

                    if (pair!.isShowAcceptBtn) {
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: CustomButton(
                              contentAlignment: Alignment.center,
                              onPressed: () {
                                showConfirmDialog(
                                  context,
                                  title: "Accept pair",
                                  content:
                                      "Are you sure you want to accept this pair?",
                                  onAccept: () {
                                    getIt
                                        .get<CompanyDetailViewModel>()
                                        .acceptPair(onSuccess: () {
                                      showSuccessBanner(
                                          content:
                                              "Pair accepted successfully");
                                    }, onFailure: (e) {
                                      showErrorBanner(content: e);
                                    });
                                  },
                                );
                              },
                              color: orangePink,
                              label: "Accept pair",
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            child: CustomButton(
                              contentAlignment: Alignment.center,
                              onPressed: () {
                                showConfirmDialog(
                                  context,
                                  title: "Decline request",
                                  content:
                                      "Are you sure you want to decline this request?",
                                  onAccept: () {
                                    getIt
                                        .get<CompanyDetailViewModel>()
                                        .declinePair(onSuccess: () {
                                      showSuccessBanner(
                                          content:
                                              "Decline request successfully");
                                    }, onFailure: (e) {
                                      showErrorBanner(content: e);
                                    });
                                  },
                                );
                              },
                              color: Colors.grey,
                              label: "Decline request",
                            ),
                          )
                        ],
                      );
                    }

                    if (pair!.isShowMakePairAgainBtn) {
                      return Column(
                        children: [
                          CustomText(
                            "You canceled this pair, you can make pair again!",
                            style: AppTextStyle.defaultStyle
                                .copyWith(fontStyle: FontStyle.italic),
                            color: Colors.grey,
                            padding: const EdgeInsets.only(bottom: 12),
                          ),
                          CustomButton(
                            contentAlignment: Alignment.center,
                            width: double.infinity,
                            onPressed: () {
                              showConfirmDialog(
                                context,
                                title: "Make pair again",
                                content:
                                    "Are you sure you want to make pair again?",
                                onAccept: () {
                                  getIt
                                      .get<CompanyDetailViewModel>()
                                      .acceptPair(onSuccess: () {
                                    showSuccessBanner(
                                        content:
                                            "Make pair again successfully");
                                  }, onFailure: (e) {
                                    showErrorBanner(content: e);
                                  });
                                },
                              );
                            },
                            color: orangePink,
                            label: "Make pair again",
                          ),
                        ],
                      );
                    }

                    if (pair!.isRequestToCompany) {
                      return CustomText(
                        "Your request is pending, please wait for the response from the company!",
                        style: AppTextStyle.defaultStyle
                            .copyWith(fontStyle: FontStyle.italic),
                        color: Colors.grey,
                      );
                    }

                    if (pair!.isCanceledByCompany) {
                      return CustomText(
                        "This pair has been canceled by the company! You can't do anything!",
                        style: AppTextStyle.defaultStyle
                            .copyWith(fontStyle: FontStyle.italic),
                        color: Colors.grey,
                      );
                    }

                    return EMPTY_WIDGET;
                  },
                )
            ],
          ),
        ),
      ),
    );
  }
}
