import 'dart:math';

import 'package:flutter/material.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/routes.dart';
import 'package:pbl5/screens/job_detail/widgets/job_detail_bottom_sheet.dart';

//
// class ExampleCard extends StatelessWidget {
//   final ExampleCandidateModel candidate;
//
//   const ExampleCard(
//     this.candidate, {
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       clipBehavior: Clip.hardEdge,
//       decoration: BoxDecoration(
//         borderRadius: const BorderRadius.all(Radius.circular(10)),
//         color: Colors.white,
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.2),
//             spreadRadius: 3,
//             blurRadius: 7,
//             offset: const Offset(0, 3),
//           ),
//         ],
//       ),
//       alignment: Alignment.center,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Flexible(
//             child: Container(
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.topCenter,
//                   end: Alignment.bottomCenter,
//                   colors: candidate.color,
//                 ),
//               ),
//             ),
//           ),
//           Padding(
//             padding: const EdgeInsets.all(16),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   candidate.name,
//                   style: const TextStyle(
//                     color: Colors.black,
//                     fontWeight: FontWeight.bold,
//                     fontSize: 20,
//                   ),
//                 ),
//                 const SizedBox(height: 5),
//                 Text(
//                   candidate.job,
//                   style: const TextStyle(
//                     color: Colors.grey,
//                     fontSize: 15,
//                   ),
//                 ),
//                 const SizedBox(height: 5),
//                 Text(
//                   candidate.city,
//                   style: const TextStyle(color: Colors.grey),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

final companyImage = [
  'assets/images/company1.jpeg',
  'assets/images/company2.png',
  'assets/images/company3.jpeg',
  'assets/images/company4.png',
  'assets/images/company5.png',
];

class CompanyCard extends StatelessWidget {
  final Company company;

  const CompanyCard(
    this.company, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Show job detail modal if there's a job, otherwise go to company detail
        if (company.applicationPositions.isNotEmpty) {
          JobDetailBottomSheet.show(
            context,
            company: company,
            job: company.applicationPositions.first,
          );
        } else {
          Navigator.of(context).pushNamed(
            Routes.companyDetail,
            arguments: company.id ?? '',
          );
        }
      },
      child: Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 3,
              blurRadius: 7,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        alignment: Alignment.center,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: _getCompanyImageProvider(),
                  fit: BoxFit.cover,
                )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Job title (if available) or company name
                  Text(
                    company.applicationPositions.isNotEmpty
                        ? company.applicationPositions.first
                                .applyPositionTitle ??
                            company.companyName ??
                            ''
                        : company.companyName ?? '',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                  const SizedBox(height: 5),
                  // Company name (if job title is shown above)
                  if (company.applicationPositions.isNotEmpty) ...[
                    Text(
                      company.companyName ?? '',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 15,
                      ),
                    ),
                    const SizedBox(height: 5),
                  ],
                  // Address or similarity score
                  Text(
                    company.similarity != null
                        ? 'Similarity: ${(company.similarity! * 100).toStringAsFixed(1)}%'
                        : company.address ?? '',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ImageProvider<Object> _getCompanyImageProvider() {
    // Check if avatar URL is valid
    if (company.avatar == null ||
        company.avatar!.isEmpty ||
        company.avatar!.trim().isEmpty ||
        company.avatar == "file:///" ||
        company.avatar!.startsWith("file:///")) {
      return AssetImage(companyImage[Random().nextInt(companyImage.length)]);
    }

    // Additional URL validation
    Uri? uri = Uri.tryParse(company.avatar!);
    if (uri == null ||
        (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https'))) {
      return AssetImage(companyImage[Random().nextInt(companyImage.length)]);
    }

    return NetworkImage(company.avatar!);
  }
}
