import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart' as painting;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/locator_config.dart';
// import 'package:pbl5/models/application_position/application_position.dart'; // Commented out - no data available
import 'package:pbl5/models/language/language.dart';
import 'package:pbl5/models/user_awards/user_awards.dart';
import 'package:pbl5/models/user_educations/user_educations.dart';
import 'package:pbl5/models/user_experiences/user_experiences.dart';
import 'package:pbl5/routes.dart';
import 'package:pbl5/screens/base/base_view.dart';
// import 'package:pbl5/screens/edit_profile/edit_application_position.dart'; // Commented out - no data available
import 'package:pbl5/screens/edit_profile/edit_award_screen.dart';
import 'package:pbl5/screens/edit_profile/edit_basic_profile_screen.dart';
import 'package:pbl5/screens/edit_profile/edit_education_screen.dart';
import 'package:pbl5/screens/edit_profile/edit_experience_screen.dart';
import 'package:pbl5/screens/edit_profile/edit_language.dart';
import 'package:pbl5/screens/login/components/sign_in_form.dart';
import 'package:pbl5/screens/profile/widget/display_image_widget.dart';
import 'package:pbl5/screens/profile/widget/secondary_card.dart';
import 'package:pbl5/screens/profile/widget/util_big_card.dart';
import 'package:pbl5/shared_customization/extensions/build_context.ext.dart';
import 'package:pbl5/shared_customization/extensions/date_time_ext.dart';
import 'package:pbl5/shared_customization/extensions/list_ext.dart';
import 'package:pbl5/shared_customization/extensions/string_ext.dart';
import 'package:pbl5/shared_customization/helpers/dialogs/dialog_helper.dart';
import 'package:pbl5/shared_customization/helpers/image_helper.dart';
import 'package:pbl5/view_models/profile_view_model.dart';
import 'package:rive/rive.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late ProfileViewModel viewModel;

  @override
  void initState() {
    viewModel = getIt.get<ProfileViewModel>();
    if (viewModel.user == null) {
      viewModel.getProfile();
    }
    super.initState();
  }

  Future<void> _pickAndProcessResume() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
    );

    if (result != null && result.files.single.path != null) {
      File file = File(result.files.single.path!);

      final cancel = showLoading();

      await viewModel.uploadResumeForExtraction(
        file: file,
        onSuccess: () {
          cancel();
          viewModel.getProfile();
        },
        onFailure: (e) {
          cancel();
          debugPrint("Resume extraction failed: $e");
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
      canPop: false,
      viewModel: viewModel,
      appBar: AppBar(
        title: const Text(
          'JobSwipe',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: Colors.pink,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.upload_file_rounded,
              size: 30,
              color: Colors.pink,
            ),
            onPressed: _pickAndProcessResume,
          ),
          SizedBox(
            width: 10.w,
          ),
        ],
        leading: const SizedBox.shrink(),
        leadingWidth: 0,
        elevation: 0,
      ),
      mobileBuilder: (context) {
        return RefreshIndicator(
          color: Colors.pink,
          onRefresh: () async {
            viewModel.getProfile();
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              buildAvatarImage(),
                              SizedBox(width: 5.w),
                              Flexible(
                                child: InkWell(
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            EditBasicProfileScreen(
                                          viewModel: viewModel,
                                        ),
                                      ),
                                    ).then((value) {
                                      if (value == 'updateProfile') {
                                        viewModel.getProfile();
                                      }
                                    });
                                  },
                                  child: Container(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        _buildFullname(),
                                        _buildEmail(),
                                        _buildDOB(),
                                        _buildPhone(),
                                        _buildAdress(),
                                        _buildLinks(),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        _buildTitle(title: 'Overview'),
                        InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => EditBasicProfileScreen(
                                  viewModel: viewModel,
                                ),
                              ),
                            ).then((value) {
                              if (value == 'updateProfile') {
                                viewModel.getProfile();
                              }
                            });
                          },
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            margin: EdgeInsets.symmetric(
                              vertical: 10.h,
                              horizontal: 10.w,
                            ),
                            padding: EdgeInsets.symmetric(
                              vertical: 16.h,
                              horizontal: 10.w,
                            ),
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.5),
                                  spreadRadius: 2,
                                  blurRadius: 3,
                                  offset: Offset(0, 3),
                                ),
                              ],
                              gradient: painting.LinearGradient(
                                end: Alignment.topLeft,
                                begin: Alignment.bottomRight,
                                stops: [0.0, 1.3],
                                colors: [
                                  Color(0xFFFFEBB2),
                                  Colors.white,
                                ],
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(15.r)),
                            ),
                            child: _buildSummaryInfo(),
                          ),
                        ),
                        _buildTitle(title: 'Background'),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildEducationCard(),
                              _buildExperienceCard(),
                              _buildAwardCard(),
                              // _buildPositionCard(), // Commented out - no data available
                              _buildLanguageCard(),
                            ],
                          ),
                        ),
                        _buildTitle(title: 'Setting'),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          child: Column(
                            children: [
                              _buildChangePasswordButton(),
                              _buildLogoutButton(context),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 100.h,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  InkWell _buildChangePasswordButton() {
    return InkWell(
      onTap: () {
        context.pushNamed(Routes.changePassword);
      },
      child: SecondaryCard(
        title: 'Change Password',
        icondata: Icons.key,
        color: Color(0xFFFFADA5),
      ),
    );
    //#f77d8e, #ff8d77, #fca463, #edbc5a, #d4d462
  }

  Builder _buildLanguageCard() {
    return Builder(builder: (context) {
      final listLanguages = context.select<ProfileViewModel, List<Language>?>(
          (vm) => vm.user?.languages);

      final allLanguageDetail = listLanguages
          ?.map(
            (e) => Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildRichKeyValue(
                      context, 'Language: ', e.languageName ?? ''),
                  buildRichKeyValue(context, 'Score: ', e.languageScore ?? ''),
                  buildRichKeyValue(
                      context,
                      'Certificate Date: ',
                      e.languageCertificateDate?.toDateTime.toDayMonthYear() ??
                          ''),
                  Divider(
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          )
          .toList();
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EditLanguageScreen(
                viewModel: viewModel,
              ),
            ),
          ).then((value) {
            if (value == 'updateLanguage') {
              viewModel.getProfile();
            }
          });
        },
        child: UtilBigCard(
          scrollController: ScrollController(),
          color: lightBlue,
          title: "Language",
          child: Column(
            children: allLanguageDetail ?? [],
          ),
        ),
      );
    });
  }

  // Commented out - no application position data available
  // Builder _buildPositionCard() {
  //   return Builder(builder: (context) {
  //     final applicationPositions =
  //         context.select<ProfileViewModel, List<ApplicationPosition>?>(
  //             (vm) => vm.user?.applicationPositions);

  //     final applicationCards = applicationPositions?.map((applicationPosition) {
  //           final positionSkills = applicationPosition.skills;
  //           return Padding(
  //             padding: const EdgeInsets.all(12.0),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.start,
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 buildRichKeyValue(context, 'Position: ',
  //                     applicationPosition.applyPositionTitle ?? ''),
  //                 Row(
  //                   children: [
  //                     Text(
  //                       'Skills: ',
  //                       style: DefaultTextStyle.of(context).style.copyWith(
  //                             fontWeight: FontWeight.bold,
  //                             color: Colors.white,
  //                           ),
  //                     ),
  //                     Expanded(
  //                       child: Wrap(
  //                         children: positionSkills == null
  //                             ? []
  //                             : positionSkills.map((skill) {
  //                                 return Card(
  //                                   child: Padding(
  //                                     padding: const EdgeInsets.all(8),
  //                                     child: Text('${skill.skillName}'),
  //                                   ),
  //                                 );
  //                               }).toList(),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 Divider(
  //                   color: Colors.white,
  //                 ),
  //               ],
  //             ),
  //           );
  //         }).toList() ??
  //         [];
  //     return InkWell(
  //       onTap: () {
  //         Navigator.push(
  //           context,
  //           MaterialPageRoute(
  //             builder: (context) => EditApplyPositionScreen(
  //               viewModel: viewModel,
  //             ),
  //           ),
  //         ).then((value) {
  //           if (value == 'updateApplyPosition') {
  //             viewModel.getProfile();
  //           }
  //         });
  //       },
  //       child: UtilBigCard(
  //         scrollController: ScrollController(),
  //         color: lightBlue,
  //         title: "Position",
  //         child: Column(
  //           children: applicationCards,
  //         ),
  //       ),
  //     );
  //   });
  // }

  InkWell _buildLogoutButton(BuildContext context) {
    return InkWell(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text('LOG OUT'),
            scrollController: ScrollController(),
            content: Text('Are you sure you want to log out?'),
            actions: <Widget>[
              CupertinoDialogAction(
                onPressed: () {
                  viewModel.logOut(onSuccess: () {
                    setState(() {
                      context.popAndPushNamed(
                        Routes.integratedAuth,
                      );
                    });
                  }, onFailure: (error) {
                    debugPrint(error);
                    setState(() {
                      context.popAndPushNamed(
                        Routes.integratedAuth,
                      );
                    });
                  });
                },
                child: Text(
                  'Yes',
                  style: TextStyle(
                    color: Colors.red,
                  ),
                ),
              ),
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'No',
                  style: TextStyle(
                    color: Colors.black54,
                  ),
                ),
              ),
            ],
          ),
        );
      },
      child: SecondaryCard(
        title: 'Log out',
        icondata: Icons.logout_outlined,
      ),
    );
  }

  Builder _buildAwardCard() {
    return Builder(builder: (context) {
      final listUserAwards = context
          .select<ProfileViewModel, List<UserAwards>?>((vm) => vm.user?.awards);
      final allAwardDetail = listUserAwards == null
          ? <Widget>[]
          : listUserAwards
              .map((a) => Container(
                    margin: const EdgeInsets.symmetric(
                      vertical: 8,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildRichKeyValue(context, 'Certificate Name: ',
                              a.certificateName ?? ''),
                          buildRichKeyValue(context, 'Certificate Time: ',
                              a.certificateTime.toDateTime.toDayMonthYear()),
                          buildRichKeyValue(context, 'Note: ', a.note ?? ''),
                          SizedBox(
                            height: 15,
                          ),
                          Divider(
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList();
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EditAwardScreen(
                viewModel: viewModel,
              ),
            ),
          ).then((value) {
            if (value == 'updateAward') {
              viewModel.getProfile();
            }
          });
        },
        child: UtilBigCard(
          title: 'Award',
          scrollController: ScrollController(),
          child: Column(
            children: allAwardDetail,
          ),
          color: lightPink.withOpacity(0.8),
        ),
      );
    });
  }

  Builder _buildExperienceCard() {
    return Builder(builder: (context) {
      final listUserExperiences =
          context.select<ProfileViewModel, List<UserExperiences>?>(
              (vm) => vm.user?.experiences);
      final allExperienceDetail = listUserExperiences == null
          ? <Widget>[]
          : listUserExperiences
              .map((e) => Container(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildRichKeyValue(
                              context, 'Position: ', e.position ?? ''),
                          buildRichKeyValue(
                              context, 'Work Place: ', e.workPlace ?? ''),
                          buildRichKeyValue(
                              context,
                              'Start date: ',
                              e.experienceStartTime.toDateTime
                                  .toDayMonthYear()),
                          buildRichKeyValue(context, 'End date: ',
                              e.experienceEndTime.toDateTime.toDayMonthYear()),
                          buildRichKeyValue(
                              context, 'Type: ', e.experienceTitle ?? ''),
                          buildRichKeyValue(context, 'Note: ', e.note ?? ''),
                          SizedBox(
                            height: 15,
                          ),
                          Divider(
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList();
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EditExperienceScreen(
                viewModel: viewModel,
              ),
            ),
          ).then((value) {
            if (value == 'updateExperience') {
              viewModel.getProfile();
            }
          });
        },
        child: UtilBigCard(
          title: 'Experience',
          scrollController: ScrollController(),
          child: Column(
            children: allExperienceDetail,
          ),
          color: lightBlue,
        ),
      );
    });
  }

  Builder _buildEducationCard() {
    return Builder(builder: (context) {
      final listEducation =
          context.select<ProfileViewModel, List<UserEducations>?>(
              (vm) => vm.user?.educations);

      final allStudyDetail = listEducation == null
          ? <Widget>[]
          : listEducation
              .map(
                (e) => Container(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildRichKeyValue(
                            context, 'Study place: ', e.studyPlace ?? ''),
                        buildRichKeyValue(
                            context, 'Majority: ', e.majority ?? ''),
                        buildRichKeyValue(
                            context,
                            'Start date: ',
                            e.studyStartTime?.toDateTime?.toDayMonthYear() ??
                                ''),
                        buildRichKeyValue(context, 'End date: ',
                            e.studyEndTime?.toDateTime?.toDayMonthYear() ?? ''),
                        buildRichKeyValue(
                            context, 'CPA: ', e.cpa?.toString() ?? ''),
                        buildRichKeyValue(context, 'Note: ', e.note ?? ''),
                        SizedBox(
                          height: 15,
                        ),
                        Divider(color: Colors.white),
                      ],
                    ),
                  ),
                ),
              )
              .toList();
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EditEducationScreen(
                viewModel: viewModel,
              ),
            ),
          ).then((value) {
            if (value == 'updateEducation') {
              viewModel.getProfile();
            }
          });
        },
        child: UtilBigCard(
          title: 'Education',
          scrollController: ScrollController(),
          color: lightPink.withOpacity(0.8),
          child: Column(
            children: allStudyDetail,
          ),
        ),
      );
    });
  }

  RichText buildRichKeyValue(
      BuildContext context, String title, String content) {
    return RichText(
      text: TextSpan(
        text: title,
        style: DefaultTextStyle.of(context).style.copyWith(
              fontSize: 15.sp,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
        children: <TextSpan>[
          TextSpan(
              text: content,
              style: DefaultTextStyle.of(context).style.copyWith(
                    fontSize: 15.sp,
                    color: Colors.white,
                  )),
        ],
      ),
    );
  }

  Builder _buildSummaryInfo() {
    return Builder(builder: (context) {
      final userSI = context.select<ProfileViewModel, String?>(
          (vm) => vm.user?.summaryIntroduction);

      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        child: Text(
          '\t\t\t' + (userSI ?? ''),
          style: TextStyle(
            fontSize: 13.sp,
            color: Colors.black87,
          ),
        ),
      );
    });
  }

  Padding _buildTitle({
    required String title,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 7.h,
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 17.sp,
          color: Colors.pinkAccent.shade700,
          fontFamily: 'Poppins',
        ),
      ),
    );
  }

  Builder _buildAdress() {
    return Builder(builder: (context) {
      final address =
          context.select<ProfileViewModel, String?>((vm) => vm.user?.address);

      // Hide if no address data
      if (address == null || address.trim().isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.location_on_rounded,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                address,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder _buildPhone() {
    return Builder(builder: (context) {
      final userPhone = context
          .select<ProfileViewModel, String?>((vm) => vm.user?.phoneNumber);

      // Hide if no phone data
      if (userPhone == null || userPhone.trim().isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.phone,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                userPhone,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder _buildDOB() {
    return Builder(builder: (context) {
      final dob =
          context.select<ProfileViewModel, String?>((vm) => vm.user?.dob);

      final dobText = dob != null ? dob.toDateTime.toDayMonthYear() : '';

      // Hide if no DOB data
      if (dobText.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.date_range,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                dobText,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder _buildLinks() {
    return Builder(builder: (context) {
      final socialMediaLinks = context.select<ProfileViewModel, List<String>?>(
          (vm) => vm.user?.socialMediaLink);

      // Filter out empty links and join
      final validLinks =
          socialMediaLinks?.where((link) => link.trim().isNotEmpty).toList();
      final allSocialMediaLinks = validLinks?.join(', ');

      // Hide if no links data
      if (allSocialMediaLinks == null || allSocialMediaLinks.trim().isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.link,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                allSocialMediaLinks,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder _buildEmail() {
    return Builder(builder: (context) {
      final userEmail =
          context.select<ProfileViewModel, String?>((vm) => vm.user?.email);

      // Hide if no email data
      if (userEmail == null || userEmail.trim().isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.mail,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                userEmail,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder _buildFullname() {
    return Builder(builder: (context) {
      final userFirstName =
          context.select<ProfileViewModel, String?>((vm) => vm.user?.firstName);
      final userLastName =
          context.select<ProfileViewModel, String?>((vm) => vm.user?.lastName);

      final fullName =
          ((userLastName ?? '').trim() + " " + (userFirstName ?? '').trim())
              .trim();

      // Hide if no name data
      if (fullName.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: EdgeInsets.only(top: 6.h),
        child: Row(
          children: [
            Icon(
              Icons.account_circle,
              color: Colors.pinkAccent.shade700,
              size: 18.r,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                fullName,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      );
    });
  }

  Builder buildAvatarImage() {
    return Builder(
      builder: (context) {
        final userImage =
            context.select<ProfileViewModel, String?>((vm) => vm.user?.avatar);
        final gender =
            context.select<ProfileViewModel, bool?>((vm) => vm.user?.gender);
        return InkWell(
          onTap: () async {
            final List<File> file = await ImagePickerHelper.showImagePicker(
                context: context,
                multiSelection: false,
                withCameraOption: true);
            if (file.isNotEmptyOrNull) viewModel.updateAvatar(file: file.first);
          },
          child: DisplayImage(
            urlPath: userImage,
            gender: gender,
            onPressed: () {},
          ),
        );
      },
    );
  }
}

class CustomPositioned extends StatelessWidget {
  final Widget child;
  const CustomPositioned({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Column(
        children: [
          Spacer(),
          SizedBox(
            height: 100,
            width: 100,
            child: child,
          ),
          Spacer(flex: 2),
        ],
      ),
    );
  }
}
