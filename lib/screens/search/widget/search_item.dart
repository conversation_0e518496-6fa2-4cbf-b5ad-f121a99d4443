import 'package:flutter/material.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/routes.dart';

class SearchItem extends StatelessWidget {
  final Company item;
  final String? searchText;

  const SearchItem({
    Key? key,
    required this.item,
    this.searchText,
  }) : super(key: key);

  TextSpan _highlightText(String text, String? highlight) {
    if (highlight == null || highlight.isEmpty) {
      return TextSpan(text: text, style: TextStyle(color: Colors.black));
    }

    String pattern = highlight.toLowerCase();
    RegExp regExp = RegExp(pattern, caseSensitive: false);

    List<TextSpan> spans = [];
    text.splitMapJoin(
      regExp,
      onMatch: (match) {
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.pink),
        ));
        return match.group(0)!;
      },
      onNonMatch: (nonMatch) {
        spans.add(
            TextSpan(text: nonMatch, style: TextStyle(color: Colors.black)));
        return nonMatch;
      },
    );

    return TextSpan(children: spans);
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        Navigator.of(context).pushNamed(
          Routes.companyDetail,
          arguments: item.id ?? '',
        );
      },
      leading: _buildAvatar(),
      title: RichText(
        text: _highlightText(item.companyName ?? '', searchText),
      ),
      subtitle: RichText(
        text: _highlightText(item.email ?? '', searchText),
      ),
    );
  }

  Widget _buildAvatar() {
    // Check if avatar URL is valid
    if (item.avatar == null ||
        item.avatar!.isEmpty ||
        item.avatar!.trim().isEmpty ||
        item.avatar == "file:///" ||
        item.avatar!.startsWith("file:///")) {
      return CircleAvatar(
        child: Icon(Icons.person),
      );
    }

    // Additional URL validation
    Uri? uri = Uri.tryParse(item.avatar!);
    if (uri == null ||
        (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https'))) {
      return CircleAvatar(
        child: Icon(Icons.person),
      );
    }

    return CircleAvatar(
      backgroundImage: NetworkImage(item.avatar!),
      onBackgroundImageError: (exception, stackTrace) {
        // This will be handled by the fallback below
      },
      child: null, // Will show the image if loaded successfully
    );
  }
}
