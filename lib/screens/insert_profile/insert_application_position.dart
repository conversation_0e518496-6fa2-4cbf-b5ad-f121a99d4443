import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/app_common_data/enums/system_constant_prefix.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/models/app_data.dart';
import 'package:pbl5/models/application_position/application_position.dart';
import 'package:pbl5/models/skill/skill.dart';
import 'package:pbl5/models/system_roles_response/system_roles_response.dart';
import 'package:pbl5/screens/base/base_view.dart';
import 'package:pbl5/screens/login/components/sign_in_form.dart';
import 'package:pbl5/shared_customization/extensions/build_context.ext.dart';
import 'package:pbl5/shared_customization/extensions/list_ext.dart';
import 'package:pbl5/shared_customization/helpers/dialogs/dialog_helper.dart';
import 'package:pbl5/shared_customization/widgets/buttons/custom_button.dart';
import 'package:pbl5/shared_customization/widgets/custom_drop_down_button.dart';
import 'package:pbl5/view_models/profile_view_model.dart';
import 'package:rive/rive.dart';
import 'package:uuid/uuid.dart';

class InsertApplicationPositionScreen extends StatefulWidget {
  final ProfileViewModel viewModel;

  InsertApplicationPositionScreen({super.key, required this.viewModel});

  @override
  State<InsertApplicationPositionScreen> createState() =>
      _InsertApplicationPositionScreenState();
}

class _InsertApplicationPositionScreenState
    extends State<InsertApplicationPositionScreen> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool isShowLoading = false;
  bool isShowConfetti = false;
  late SMITrigger check;
  late SMITrigger error;
  late SMITrigger reset;
  late SMITrigger confetti;

  final positionDropdownModels = getIt
      .get<AppData>()
      .systemConstants[SystemConstantPrefix.APPLY_POSITION]!
      .map((e) => DropdownItemModel(label: e.constantName!, value: e))
      .toList();

  final salaryDropdownModels = getIt
      .get<AppData>()
      .systemConstants[SystemConstantPrefix.SALARY_RANGES]!
      .map((e) => DropdownItemModel(label: e.constantName!, value: e))
      .toList();

  final skillDropdownModels = getIt
      .get<AppData>()
      .systemConstants[SystemConstantPrefix.SKILL]!
      .map((e) => DropdownItemModel(label: e.constantName!, value: e))
      .toList();

  ApplicationPosition currentData = ApplicationPosition.empty;

  @override
  void initState() {
    super.initState();
    // Xóa giá trị của các controller khi màn hình được khởi tạo
    widget.viewModel.addPositionTitleController.clear();
    widget.viewModel.addSalaryController.clear();
    widget.viewModel.addSkillNameController.clear();

    // Khởi tạo một skill mặc định nếu chưa có
    if (currentData.skills == null || currentData.skills!.isEmpty) {
      setState(() {
        currentData = currentData
            .copyWith(skills: [Skill(id: Uuid().v4(), isGenerated: true)]);
      });
    }
  }

  void onAddApplyPosition(BuildContext context) {
    setState(() {
      isShowLoading = true;
      isShowConfetti = true;
    });
    Future.delayed(Duration(seconds: 1), () async {
      if (_formKey.currentState!.validate()) {
        await widget.viewModel.insertApplyPosition(
          onSuccess: () {
            check.fire();
            Future.delayed(Duration(seconds: 2), () {
              setState(() => isShowLoading = false);
              confetti.fire();
            }).then(
              (e) => Future.delayed(Duration(seconds: 1), () async {
                await widget.viewModel
                    .getProfile()
                    .then((value) => context.pop('addApplyPosition'));
              }),
            );
          },
          onFailure: (e) {
            showErrorDialog(context, content: e);
            error.fire();
            Future.delayed(Duration(seconds: 2), () {
              setState(() => isShowLoading = false);
            });
          },
        );
      } else {
        error.fire();
        Future.delayed(Duration(seconds: 2), () {
          setState(() => isShowLoading = false);
          confetti.fire();
        });
      }
    });
  }

  StateMachineController getRiveController(Artboard artboard) {
    StateMachineController? controller =
        StateMachineController.fromArtboard(artboard, "State Machine 1");
    artboard.addController(controller!);
    return controller;
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
      viewModel: widget.viewModel,
      backgroundColor: Colors.white,
      canPop: true,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: const Text(
          'Add Apply Positions',
          style: TextStyle(
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: orangePink),
        ),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.chevron_left, size: 30),
          onPressed: context.pop,
        ),
      ),
      mobileBuilder: (context) {
        return Stack(
          children: [
            Form(
              key: _formKey,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.h),
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextField(
                        decoration: InputDecoration(
                          labelText: "Apply Position",
                          hintText: "Enter position title",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        controller: widget.viewModel.addPositionTitleController,
                        onChanged: (value) {
                          setState(() {
                            currentData =
                                currentData.copyWith(applyPositionTitle: value);
                          });
                        },
                      ),
                      SizedBox(height: 12),
                      TextField(
                        decoration: InputDecoration(
                          labelText: "Salary Range",
                          hintText: "Enter salary range",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        controller: widget.viewModel.addSalaryController,
                        onChanged: (value) {
                          setState(() {
                            currentData = currentData.copyWith(salary: value);
                          });
                        },
                      ),
                      SizedBox(height: 12),
                      ...(currentData.skills ?? [])
                          .asMap()
                          .entries
                          .map(
                            (skillEntry) => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        decoration: InputDecoration(
                                          labelText:
                                              "Skill ${skillEntry.key + 1}",
                                          hintText: "Enter skill name",
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                        controller: skillEntry.key == 0
                                            ? widget.viewModel
                                                .addSkillNameController
                                            : TextEditingController(
                                                text: skillEntry
                                                        .value.skillName ??
                                                    '',
                                              ),
                                        onChanged: (value) {
                                          setState(() {
                                            currentData = currentData.copyWith(
                                                skills:
                                                    currentData.skills
                                                        .update(
                                                            (e) => e.copyWith(
                                                                skillName:
                                                                    value),
                                                            (e) =>
                                                                e.id ==
                                                                skillEntry
                                                                    .value.id));

                                            // Cập nhật controller cho skill đầu tiên
                                            if (skillEntry.key == 0) {
                                              widget
                                                  .viewModel
                                                  .addSkillNameController
                                                  .text = value;
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                    if (skillEntry.value.isGenerated)
                                      IconButton(
                                        icon: Icon(Icons.delete,
                                            color: Colors.red),
                                        onPressed: () {
                                          setState(() {
                                            currentData = currentData.copyWith(
                                                skills: currentData.skills
                                                    .deleteAt(skillEntry.key));
                                          });
                                        },
                                      ),
                                  ],
                                ),
                                SizedBox(height: 8),
                              ],
                            ),
                          )
                          .toList(),
                      CustomButton(
                        onPressed: () {
                          setState(() {
                            currentData = currentData.copyWith(
                                skills: currentData.skills.insertLast(
                                    Skill(id: Uuid().v4(), isGenerated: true)));
                          });
                        },
                        label: "Add Skill",
                        color: const Color(0xFFF77D8E),
                        margin: const EdgeInsets.only(top: 12),
                      ),
                      SizedBox(height: 15.h),
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0, bottom: 0),
                        child: ElevatedButton.icon(
                          onPressed: () => onAddApplyPosition(context),
                          style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFF77D8E),
                              minimumSize: const Size(double.infinity, 56),
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10),
                                      topRight: Radius.circular(25),
                                      bottomRight: Radius.circular(25),
                                      bottomLeft: Radius.circular(25)))),
                          icon: const Icon(CupertinoIcons.arrow_right,
                              color: Colors.white),
                          label: Text(
                            "ADD",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontFamily: "Poppins",
                              wordSpacing: 1.2,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            isShowLoading
                ? CustomPositioned(
                    child: RiveAnimation.asset(
                    "assets/RiveAssets/check.riv",
                    onInit: (artboard) {
                      StateMachineController controller =
                          getRiveController(artboard);
                      check = controller.findSMI("Check") as SMITrigger;
                      error = controller.findSMI("Error") as SMITrigger;
                      reset = controller.findSMI("Reset") as SMITrigger;
                    },
                  ))
                : const SizedBox(),
            isShowConfetti
                ? CustomPositioned(
                    child: Transform.scale(
                    scale: 6,
                    child: RiveAnimation.asset(
                      "assets/RiveAssets/confetti.riv",
                      onInit: (artboard) {
                        StateMachineController controller =
                            getRiveController(artboard);
                        confetti = controller.findSMI("Trigger explosion")
                            as SMITrigger;
                      },
                    ),
                  ))
                : const SizedBox()
          ],
        );
      },
    );
  }
}
