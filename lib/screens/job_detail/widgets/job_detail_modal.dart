import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/models/application_position/application_position.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/routes.dart';

class JobDetailModal extends StatelessWidget {
  final Company company;
  final ApplicationPosition job;
  final bool disableCompanyNavigation;

  const JobDetailModal({
    super.key,
    required this.company,
    required this.job,
    this.disableCompanyNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.chevron_left, size: 28),
                ),
                Text(
                  'Job Detail',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    letterSpacing: 0.2,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Share functionality
                  },
                  icon: const Icon(Icons.share, size: 24),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16.h),

                  // Company logo and job info
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Company logo
                      GestureDetector(
                        onTap: disableCompanyNavigation
                            ? null
                            : () {
                                // Close the modal first, then navigate to company detail
                                Navigator.of(context).pop();
                                Navigator.of(context).pushNamed(
                                  Routes.companyDetail,
                                  arguments: company.id ?? '',
                                );
                              },
                        child: Container(
                          width: 60.w,
                          height: 60.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: _buildCompanyAvatar(),
                          ),
                        ),
                      ),

                      SizedBox(width: 16.w),

                      // Job title and company name
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              job.applyPositionTitle ?? 'Job Title',
                              style: TextStyle(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Inter',
                                letterSpacing: 0.3,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              company.companyName ?? 'Company Name',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.grey.shade600,
                                fontFamily: 'Inter',
                                letterSpacing: 0.2,
                              ),
                            ),
                            if (company.address?.isNotEmpty == true) ...[
                              SizedBox(height: 4.h),
                              Text(
                                company.address!,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey.shade500,
                                  fontFamily: 'Inter',
                                  letterSpacing: 0.1,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Bookmark icon
                      IconButton(
                        onPressed: () {
                          // Bookmark functionality
                        },
                        icon: const Icon(Icons.bookmark_border),
                      ),
                    ],
                  ),

                  SizedBox(height: 20.h),

                  // Skills section - positioned like in the reference image
                  _buildSkillsSection(),

                  // Job description section
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'About this role',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Inter',
                            letterSpacing: 0.3,
                          ),
                        ),
                        SizedBox(height: 12.h),
                        _buildFormattedDescription(
                          job.description?.isNotEmpty == true
                              ? job.description!
                              : 'No description available for this position.',
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 100.h), // Space for the apply button
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormattedDescription(String description) {
    // First, handle semicolon formatting
    String processedDescription = _processSemicolonFormatting(description);

    // Then split by common section markers
    List<String> sections = [];

    // Split by ** markers first (markdown headers)
    List<String> markdownSections =
        processedDescription.split(RegExp(r'\*\*[^*]+\*\*'));
    List<String> headers = RegExp(r'\*\*([^*]+)\*\*')
        .allMatches(processedDescription)
        .map((match) => '**${match.group(1)}**')
        .toList();

    // Reconstruct with headers
    for (int i = 0; i < markdownSections.length; i++) {
      if (i > 0 && i - 1 < headers.length) {
        sections.add(headers[i - 1]);
      }
      if (markdownSections[i].trim().isNotEmpty) {
        sections.add(markdownSections[i].trim());
      }
    }

    // If no markdown sections found, split by sentences and group logically
    if (sections.length <= 1) {
      sections = _splitIntoLogicalSections(processedDescription);
    }

    // Further split each section into paragraphs
    List<String> paragraphs = [];
    for (String section in sections) {
      List<String> sectionParagraphs = _splitSectionIntoParagraphs(section);
      paragraphs.addAll(sectionParagraphs);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paragraphs.map((paragraph) {
        // Check if paragraph is indented content (from semicolon formatting)
        bool isIndented = paragraph.trim().startsWith('    ');
        String displayText = paragraph;

        // Check if it's a sub-header (indented and contains bold markdown)
        bool isSubHeader =
            isIndented && paragraph.contains('**') && paragraph.contains(':');

        // Check if paragraph is a header (starts with * or contains specific keywords)
        bool isHeader = _isHeaderParagraph(displayText);

        // Check if paragraph is a bullet point (after cleaning)
        String cleanedForCheck = _cleanText(displayText);
        bool isBulletPoint = cleanedForCheck.startsWith('•') ||
            (!_isHeaderParagraph(displayText) &&
                !isSubHeader &&
                (displayText.trim().startsWith('*') ||
                    displayText.trim().startsWith('-') ||
                    RegExp(r'^\d+\.').hasMatch(displayText.trim())));

        return Padding(
          padding: EdgeInsets.only(
            bottom: (isHeader || isSubHeader) ? 12.h : 8.h,
            left: isBulletPoint ? 16.w : (isIndented ? 24.w : 0),
          ),
          child: _buildFormattedText(displayText, isHeader, isSubHeader),
        );
      }).toList(),
    );
  }

  String _cleanText(String text) {
    String cleaned = text;

    // Handle markdown bold headers first (remove ** from headers only)
    if (_isHeaderParagraph(text)) {
      cleaned = cleaned
          .replaceAll(RegExp(r'^\*\*'), '') // Remove leading **
          .replaceAll(RegExp(r'\*\*$'), '') // Remove trailing **
          .replaceAll(
              RegExp(r'^\*+\s*'), '') // Remove leading * with optional spaces
          .replaceAll(
              RegExp(r'\s*\*+$'), '') // Remove trailing * with optional spaces
          .replaceAll(
              RegExp(r'\*+'), ''); // Remove any remaining asterisks in headers
    }

    // Fix escaped characters
    cleaned = cleaned
        .replaceAll(RegExp(r'\\-'), '-') // Fix escaped dashes
        .replaceAll(RegExp(r'\\n'), '\n') // Fix escaped newlines
        .replaceAll(RegExp(r'\\\('), '(') // Fix escaped parentheses
        .replaceAll(RegExp(r'\\\)'), ')') // Fix escaped parentheses
        .replaceAll(RegExp(r'\\&'), '&') // Fix escaped ampersands
        .replaceAll(RegExp(r'\\/'), '/') // Fix escaped slashes
        .replaceAll(RegExp(r'\\,'), ',') // Fix escaped commas
        .replaceAll(RegExp(r'\\\.'), '.') // Fix escaped dots
        .replaceAll(RegExp(r'\\:'), ':') // Fix escaped colons
        .replaceAll(RegExp(r'\\;'), ';') // Fix escaped semicolons
        .replaceAll(RegExp(r'\\"'), '"') // Fix escaped quotes
        .replaceAll(RegExp(r"\\'"), "'") // Fix escaped single quotes
        .replaceAll(RegExp(r'\\'), '') // Remove remaining backslashes
        .replaceAll(
            RegExp(r'\s+'), ' ') // Replace multiple spaces with single space
        .trim();

    // Only convert to bullet points if it's NOT a header
    if (!_isHeaderParagraph(text)) {
      // Handle various bullet point formats and convert to consistent bullets
      if (cleaned.startsWith('*')) {
        // Remove all leading asterisks and convert to bullet
        cleaned = cleaned.replaceFirst(RegExp(r'^\*+\s*'), '• ');
      } else if (cleaned.startsWith('- ')) {
        cleaned = '• ${cleaned.substring(2).trim()}';
      } else if (RegExp(r'^\d+\.\s').hasMatch(cleaned)) {
        // Convert numbered lists to bullets
        cleaned = '• ${cleaned.replaceFirst(RegExp(r'^\d+\.\s'), '')}';
      }

      // Remove any remaining scattered asterisks in content (but preserve meaningful ones)
      // Remove isolated asterisks (surrounded by spaces)
      cleaned = cleaned.replaceAll(RegExp(r'\s\*\s'), ' ');
      // Remove asterisks at the beginning of words that look like bullet attempts
      cleaned = cleaned.replaceAll(RegExp(r'\s\*([a-zA-Z])'), r' $1');
      // Remove trailing asterisks that are isolated
      cleaned = cleaned.replaceAll(RegExp(r'\s\*$'), '');
    }

    // Final cleanup - remove any remaining problematic asterisks
    // Remove multiple consecutive asterisks that might have been missed
    cleaned = cleaned.replaceAll(RegExp(r'\*{2,}'), '');
    // Remove single asterisks that are clearly formatting artifacts
    cleaned = cleaned.replaceAll(RegExp(r'^\*\s*'), ''); // Leading asterisk
    cleaned = cleaned.replaceAll(RegExp(r'\s*\*\s*$'), ''); // Trailing asterisk

    // Final space cleanup
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();

    return cleaned;
  }

  bool _isHeaderParagraph(String paragraph) {
    String trimmed = paragraph.trim().toLowerCase();
    String original = paragraph.trim();

    // Check for Vietnamese job-specific headers
    List<String> vietnameseHeaders = [
      'giới thiệu',
      'mô tả công việc',
      'job description',
      'yêu cầu',
      'quyền lợi',
      'địa điểm làm việc',
      'lương',
      'loại hình công việc',
      'hạn nộp',
      'kinh nghiệm',
      'bằng cấp',
      'lĩnh vực',
      'số lượng cần tuyển',
      'giới tính',
      'your skills and experience',
      'why you\'ll love working here',
      'your goal will be',
      'in this role',
    ];

    // Check for common English header patterns
    List<String> englishHeaders = [
      'responsibilities',
      'requirements',
      'qualifications',
      'skills',
      'experience',
      'position summary',
      'role and responsibilities',
      'skills and qualifications',
      'technical requirement',
      'general requirement',
      'about',
      'job responsibilities',
      'key responsibilities',
      'what you\'ll be doing',
      'what you will be doing',
      'mandatory',
      'optional',
      'knowledge',
      'education',
      'benefits',
      'how to apply',
      'workplace type',
      'equal opportunity',
    ];

    // Combine all headers
    List<String> allHeaders = [...vietnameseHeaders, ...englishHeaders];

    // Check if paragraph exactly matches or starts with header keywords
    for (String keyword in allHeaders) {
      if (trimmed == keyword ||
          trimmed.startsWith('$keyword:') ||
          trimmed.startsWith('**$keyword') ||
          (trimmed.startsWith(keyword) &&
              trimmed.length < keyword.length + 20)) {
        return true;
      }
    }

    // Check if it's a short line that ends with colon (likely a header)
    if (trimmed.endsWith(':') &&
        trimmed.length < 50 &&
        !trimmed.contains('http')) {
      return true;
    }

    // Check if it starts with ** and ends with ** (markdown bold headers)
    if (original.startsWith('**') &&
        original.endsWith('**') &&
        original.length < 100) {
      return true;
    }

    // Check if it starts with asterisk but is short (likely a header, not bullet)
    if (trimmed.startsWith('*') &&
        !trimmed.startsWith('* ') &&
        trimmed.length < 80) {
      return true;
    }

    return false;
  }

  List<String> _splitIntoLogicalSections(String description) {
    // Split by common Vietnamese and English section indicators
    List<String> sections = [];

    // Try to split by key phrases that indicate new sections
    List<String> sectionMarkers = [
      'Job description',
      'Your skills and experience',
      'Why you\'ll love working here',
      'Địa điểm làm việc:',
      'Lương:',
      'Loại hình công việc:',
      'Yêu cầu:',
      'Quyền lợi:',
      'Hạn nộp:',
      'Kinh nghiệm:',
      'Bằng cấp:',
      'Lĩnh vực:',
    ];

    String remaining = description;

    for (String marker in sectionMarkers) {
      if (remaining.contains(marker)) {
        List<String> parts = remaining.split(marker);
        if (parts.length > 1) {
          if (parts[0].trim().isNotEmpty) {
            sections.add(parts[0].trim());
          }
          sections.add(marker);
          remaining = parts.sublist(1).join(marker);
        }
      }
    }

    if (remaining.trim().isNotEmpty) {
      sections.add(remaining.trim());
    }

    return sections.isNotEmpty ? sections : [description];
  }

  List<String> _splitSectionIntoParagraphs(String section) {
    // First, handle semicolon formatting (split content after ; and before :)
    String processedSection = _processSemicolonFormatting(section);

    // Split by double newlines first
    List<String> paragraphs = processedSection.split('\n\n');

    // If no double newlines, split by single newlines but group related content
    if (paragraphs.length == 1) {
      List<String> lines = processedSection.split('\n');
      List<String> groupedParagraphs = [];
      String currentGroup = '';

      for (String line in lines) {
        String trimmed = line.trim();
        if (trimmed.isEmpty) continue;

        // Start new paragraph if this looks like a header or bullet point
        if (_isHeaderParagraph(trimmed) ||
            trimmed.startsWith('•') ||
            trimmed.startsWith('*') ||
            trimmed.startsWith('-') ||
            trimmed.startsWith(
                '    ') || // Indented content from semicolon processing
            RegExp(r'^\d+\.').hasMatch(trimmed)) {
          if (currentGroup.isNotEmpty) {
            groupedParagraphs.add(currentGroup.trim());
            currentGroup = '';
          }
          currentGroup = trimmed;
        } else {
          // Continue current paragraph
          if (currentGroup.isNotEmpty) {
            currentGroup += ' ';
          }
          currentGroup += trimmed;
        }
      }

      if (currentGroup.isNotEmpty) {
        groupedParagraphs.add(currentGroup.trim());
      }

      return groupedParagraphs.isNotEmpty
          ? groupedParagraphs
          : [processedSection];
    }

    return paragraphs.where((p) => p.trim().isNotEmpty).toList();
  }

  String _processSemicolonFormatting(String text) {
    // Split by semicolons and process each part
    List<String> parts = text.split(';');
    if (parts.length <= 1) return text;

    List<String> processedParts = [];

    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();
      if (part.isEmpty) continue;

      if (i == 0) {
        // First part - add as is
        processedParts.add(part);
      } else {
        // Parts after semicolon - look for content ending with colon
        if (part.contains(':')) {
          int colonIndex = part.indexOf(':');
          String beforeColon = part.substring(0, colonIndex).trim();
          String afterColon = part.substring(colonIndex + 1).trim();

          // Format: newline + indented bold text + colon + normal text
          String formatted = '\n    **$beforeColon**: $afterColon';
          processedParts.add(formatted);
        } else {
          // No colon found, just add with newline and indent
          processedParts.add('\n    $part');
        }
      }
    }

    return processedParts.join('');
  }

  Widget _buildFormattedText(String text, bool isHeader, bool isSubHeader) {
    // Check if text contains markdown bold formatting
    if (text.contains('**') && text.contains(':')) {
      return _buildRichText(text, isHeader, isSubHeader);
    }

    // Regular text without markdown
    return Text(
      _cleanText(text),
      style: TextStyle(
        fontSize: isHeader ? 16.sp : (isSubHeader ? 15.sp : 14.sp),
        fontWeight:
            (isHeader || isSubHeader) ? FontWeight.w600 : FontWeight.w400,
        color:
            (isHeader || isSubHeader) ? Colors.black87 : Colors.grey.shade800,
        height: (isHeader || isSubHeader) ? 1.4 : 1.6,
        fontFamily: 'Inter',
        letterSpacing: (isHeader || isSubHeader) ? 0.2 : 0.1,
      ),
      textAlign: TextAlign.left,
    );
  }

  Widget _buildRichText(String text, bool isHeader, bool isSubHeader) {
    List<TextSpan> spans = [];

    // Split by ** to find bold sections
    List<String> parts = text.split('**');

    for (int i = 0; i < parts.length; i++) {
      String part = parts[i];
      if (part.isEmpty) continue;

      // Clean the text part
      String cleanedPart = _cleanText(part);

      if (i % 2 == 1) {
        // Odd index = bold text (between **)
        spans.add(TextSpan(
          text: cleanedPart,
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
            fontFamily: 'Inter',
            letterSpacing: 0.1,
          ),
        ));
      } else {
        // Even index = normal text
        spans.add(TextSpan(
          text: cleanedPart,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade800,
            fontFamily: 'Inter',
            letterSpacing: 0.1,
          ),
        ));
      }
    }

    return RichText(
      textAlign: TextAlign.left,
      text: TextSpan(children: spans),
    );
  }

  Widget _buildCompanyAvatar() {
    // Check if avatar URL is valid
    if (company.avatar == null ||
        company.avatar!.isEmpty ||
        company.avatar!.trim().isEmpty ||
        company.avatar == "file:///" ||
        company.avatar!.startsWith("file:///")) {
      return Icon(Icons.business, size: 30.w, color: Colors.grey.shade600);
    }

    // Additional URL validation
    Uri? uri = Uri.tryParse(company.avatar!);
    if (uri == null ||
        (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https'))) {
      return Icon(Icons.business, size: 30.w, color: Colors.grey.shade600);
    }

    return Image.network(
      company.avatar!,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
            strokeWidth: 2,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Icon(Icons.business, size: 30.w, color: Colors.grey.shade600);
      },
    );
  }

  Widget _buildSkillsSection() {
    // Get skills from job
    List<String> skillNames = [];

    if (job.skills?.isNotEmpty == true) {
      skillNames = job.skills!
          .map((skill) => skill.skillName ?? '')
          .where((name) => name.isNotEmpty)
          .toList();
    }

    // If no skills available, don't show the section
    if (skillNames.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Skills title
        Text(
          'Required Skills',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            fontFamily: 'Inter',
            letterSpacing: 0.3,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12.h),

        // Skills chips
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: skillNames.map((skillName) {
            return Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFFFEBB2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(width: 2, color: const Color(0xFFFFF8E1)),
              ),
              child: Text(
                skillName,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.grey.shade600,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.1,
                ),
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 24.h),
      ],
    );
  }
}
