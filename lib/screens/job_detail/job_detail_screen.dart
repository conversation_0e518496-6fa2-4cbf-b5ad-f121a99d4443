import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pbl5/screens/base/base_view.dart';
import 'package:pbl5/shared_customization/extensions/build_context.ext.dart';

import '../../constants.dart';
import '../../locator_config.dart';
import '../../view_models/company_detail_view_model.dart';

class JobDetailScreen extends StatefulWidget {
  final String applicationPositionId;
  const JobDetailScreen({super.key, required this.applicationPositionId});

  @override
  State<JobDetailScreen> createState() => _JobDetailScreenState();
}

class _JobDetailScreenState extends State<JobDetailScreen> {
  late final CompanyDetailViewModel viewModel;

  @override
  void initState() {
    viewModel = getIt.get<CompanyDetailViewModel>();
    viewModel.initState(userId: widget.applicationPositionId);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
      viewModel: viewModel,
      backgroundColor: orangePink,
      canPop: true,
      appBar: AppBar(
        backgroundColor: orangePink,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.chevron_left,
            size: 30,
            color: Colors.white,
          ),
          onPressed: () {
            context.pop();
            viewModel.clear();
          },
        ),
      ),
      mobileBuilder: (context) {
        var company = context.select((CompanyDetailViewModel vm) => vm.company);
        return company == null
            ? Center(child: Container())
            : Column(
                children: [
                  Expanded(
                    flex: 1,
                    child: Container(
                      child: Column(
                        children: [],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Container(
                        color: Colors.white,
                        child: Column(
                          children: [],
                        )),
                  ),
                ],
              );
      },
    );
  }
}
