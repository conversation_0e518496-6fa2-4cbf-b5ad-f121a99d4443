import 'package:flutter/foundation.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/models/user/user.dart';
import 'package:pbl5/models/user_embedding_data/user_embedding_models.dart';
import 'package:pbl5/services/apiAI/api_ai.dart';
import 'package:pbl5/services/background_task_service.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/storages.dart';
import 'package:pbl5/utils/user_data_comparison.dart';
import 'package:pbl5/shared_customization/helpers/utilizations/dio_parse_error.dart';

/// Service to manage user vector embedding updates
class UserEmbeddingService {
  static final UserEmbeddingService _instance =
      UserEmbeddingService._internal();
  factory UserEmbeddingService() => _instance;
  UserEmbeddingService._internal();

  final BackgroundTaskManager _taskManager = BackgroundTaskManager();
  final CustomSharedPreferences _storage = getIt.get<CustomSharedPreferences>();
  final ApiAI _apiAI = getIt.get<ApiAI>();

  static const String _embeddingTaskId = 'user_embedding_update';
  static const String _defaultApiKey = 'default';

  // Global debouncing to prevent multiple triggers
  String? _lastProcessedUserId;
  DateTime? _lastProcessedTime;
  String? _lastProcessedDataHash;
  static const Duration _globalDebounceInterval = Duration(seconds: 5);

  // Navigation debouncing to prevent multiple triggers from same navigation
  String? _lastNavigationUserId;
  DateTime? _lastNavigationCheck;
  static const Duration _navigationDebounceInterval = Duration(seconds: 3);

  /// Check if user data has changed and trigger embedding update if needed
  Future<void> checkAndUpdateEmbedding(
    User user, {
    bool forceUpdate = false,
    VoidCallback? onSuccess,
    Function(String error)? onError,
  }) async {
    try {
      debugPrint('UserEmbeddingService: Checking user data for changes...');

      // Create current snapshot
      final currentSnapshot = UserDataComparator.createSnapshot(user);

      // Get previous snapshot
      final previousSnapshot = _storage.getUserDataSnapshot();

      // Check if data has changed
      bool hasChanged =
          forceUpdate || _hasDataChanged(currentSnapshot, previousSnapshot);

      if (!hasChanged) {
        debugPrint(
            'UserEmbeddingService: No changes detected, skipping embedding update');
        onSuccess?.call();
        return;
      }

      debugPrint(
          'UserEmbeddingService: Data changes detected, scheduling embedding update');

      // Schedule background embedding update
      await _scheduleEmbeddingUpdate(
        user: user,
        snapshot: currentSnapshot,
        onSuccess: onSuccess,
        onError: onError,
      );
    } catch (error) {
      final errorMessage =
          'Failed to check user data changes: ${parseError(error)}';
      debugPrint('UserEmbeddingService: $errorMessage');
      onError?.call(errorMessage);
    }
  }

  /// Optimized embedding check with additional smart logic
  /// This method includes extra optimizations for minimal embedding calls
  Future<void> checkAndUpdateEmbeddingOptimized(
    User user, {
    bool forceUpdate = false,
    VoidCallback? onSuccess,
    Function(String error)? onError,
  }) async {
    try {
      debugPrint('UserEmbeddingService: ===== OPTIMIZED EMBEDDING CHECK =====');
      debugPrint('UserEmbeddingService: User ID: ${user.id}');
      debugPrint('UserEmbeddingService: Force update: $forceUpdate');

      // Skip if already running to prevent duplicate calls
      if (isEmbeddingUpdateRunning()) {
        debugPrint(
            'UserEmbeddingService: ⏰ TASK ALREADY RUNNING - Skipping duplicate call');
        debugPrint('UserEmbeddingService: ===== END OPTIMIZED CHECK =====');
        onSuccess?.call();
        return;
      }

      // Global debouncing check to prevent rapid successive calls
      final now = DateTime.now();
      final userId = user.id ?? '';
      final currentSnapshot = UserDataComparator.createSnapshot(user);
      final currentDataHash = currentSnapshot.dataHash;

      debugPrint('UserEmbeddingService: Debouncing check:');
      debugPrint('UserEmbeddingService: - Force update: $forceUpdate');
      debugPrint(
          'UserEmbeddingService: - Last processed user: $_lastProcessedUserId');
      debugPrint('UserEmbeddingService: - Current user: $userId');
      debugPrint(
          'UserEmbeddingService: - Last processed hash: $_lastProcessedDataHash');
      debugPrint('UserEmbeddingService: - Current hash: $currentDataHash');
      debugPrint(
          'UserEmbeddingService: - Last processed time: $_lastProcessedTime');
      debugPrint('UserEmbeddingService: - Current time: $now');

      if (!forceUpdate &&
          _lastProcessedUserId == userId &&
          _lastProcessedDataHash == currentDataHash &&
          _lastProcessedTime != null &&
          now.difference(_lastProcessedTime!).inSeconds <
              _globalDebounceInterval.inSeconds) {
        debugPrint(
            'UserEmbeddingService: ⏰ GLOBAL DEBOUNCE - Skipping call (${now.difference(_lastProcessedTime!).inSeconds}s < ${_globalDebounceInterval.inSeconds}s)');
        debugPrint('UserEmbeddingService: Same data hash: $currentDataHash');
        debugPrint('UserEmbeddingService: ===== END OPTIMIZED CHECK =====');
        onSuccess?.call();
        return;
      }

      // Get previous snapshot
      final previousSnapshot = _storage.getUserDataSnapshot();

      // Enhanced change detection with significance check
      bool hasSignificantChanges = forceUpdate ||
          _hasSignificantDataChanges(currentSnapshot, previousSnapshot);

      if (!hasSignificantChanges) {
        debugPrint(
            'UserEmbeddingService: ✅ NO SIGNIFICANT CHANGES - Skipping embedding update');
        debugPrint('UserEmbeddingService: ===== END OPTIMIZED CHECK =====');
        onSuccess?.call();
        return;
      }

      // Update global debouncing tracking
      _lastProcessedUserId = userId;
      _lastProcessedTime = now;
      _lastProcessedDataHash = currentDataHash;

      debugPrint(
          'UserEmbeddingService: ❌ SIGNIFICANT CHANGES DETECTED - Scheduling embedding update');
      debugPrint('UserEmbeddingService: Data hash: $currentDataHash');

      // Schedule background embedding update with longer delay for optimization
      await _scheduleEmbeddingUpdate(
        user: user,
        snapshot: currentSnapshot,
        onSuccess: onSuccess,
        onError: onError,
        delay: Duration(seconds: 2), // Longer delay for optimization
      );

      debugPrint('UserEmbeddingService: ===== END OPTIMIZED CHECK =====');
    } catch (error) {
      final errorMessage =
          'Failed to run optimized embedding check: ${parseError(error)}';
      debugPrint('UserEmbeddingService: $errorMessage');
      debugPrint('UserEmbeddingService: ===== END OPTIMIZED CHECK =====');
      onError?.call(errorMessage);
    }
  }

  /// Schedule embedding update in background
  Future<void> _scheduleEmbeddingUpdate({
    required User user,
    required UserDataSnapshot snapshot,
    VoidCallback? onSuccess,
    Function(String error)? onError,
    Duration delay = const Duration(milliseconds: 500),
  }) async {
    await _taskManager.executeTask(
      taskId: _embeddingTaskId,
      task: () => _performEmbeddingUpdate(user, snapshot),
      onSuccess: () {
        debugPrint(
            'UserEmbeddingService: Embedding update completed successfully');
        onSuccess?.call();
      },
      onError: (error) {
        debugPrint('UserEmbeddingService: Embedding update failed: $error');
        onError?.call(error);
      },
      delay: delay, // Configurable delay
      enableRetry: true,
      maxRetries: 1, // Reduce retries to prevent multiple calls
    );
  }

  /// Perform the actual embedding update API call
  Future<void> _performEmbeddingUpdate(
      User user, UserDataSnapshot snapshot) async {
    try {
      debugPrint('UserEmbeddingService: Starting embedding update API call...');

      // Make API call (no body needed, just query params)
      final response = await _apiAI.updateUserEmbedding(
        apiKey: _defaultApiKey,
        accountId: user.id ?? '',
      );

      if (response.status == 'success') {
        // Save successful update
        await _storage.saveUserDataSnapshot(snapshot);
        await _storage.saveLastEmbeddingUpdate(DateTime.now());

        debugPrint(
            'UserEmbeddingService: Embedding update API call successful');
      } else {
        throw Exception(
            'API returned unsuccessful response: ${response.message}');
      }
    } catch (error) {
      throw Exception('Embedding API call failed: ${parseError(error)}');
    }
  }

  /// Check if user data has changed compared to previous snapshot
  bool _hasDataChanged(UserDataSnapshot current, UserDataSnapshot? previous) {
    debugPrint('UserEmbeddingService: ===== DATA COMPARISON DEBUG =====');

    if (previous == null) {
      debugPrint(
          'UserEmbeddingService: No previous snapshot found, treating as changed');
      debugPrint('UserEmbeddingService: Current hash: ${current.dataHash}');
      debugPrint('UserEmbeddingService: ===== END DEBUG =====');
      return true;
    }

    final currentHash = current.dataHash;
    final previousHash = previous.dataHash;

    debugPrint('UserEmbeddingService: Previous hash: $previousHash');
    debugPrint('UserEmbeddingService: Current hash:  $currentHash');
    debugPrint(
        'UserEmbeddingService: Hashes equal: ${currentHash == previousHash}');

    final hasChanged = currentHash != previousHash;

    if (hasChanged) {
      debugPrint(
          'UserEmbeddingService: ❌ DATA CHANGED - Will trigger embedding update');
      debugPrint(
          'UserEmbeddingService: Previous snapshot timestamp: ${previous.lastUpdated}');
      debugPrint(
          'UserEmbeddingService: Current snapshot timestamp: ${current.lastUpdated}');
    } else {
      debugPrint(
          'UserEmbeddingService: ✅ NO DATA CHANGES - Skipping embedding update');
    }

    debugPrint('UserEmbeddingService: ===== END DEBUG =====');
    return hasChanged;
  }

  /// Check if user data has significant changes that warrant embedding update
  /// This method provides more intelligent filtering to reduce unnecessary embedding calls
  bool _hasSignificantDataChanges(
      UserDataSnapshot current, UserDataSnapshot? previous) {
    if (previous == null) {
      debugPrint(
          'UserEmbeddingService: No previous snapshot found, treating as significant change');
      return true;
    }

    // First check basic hash comparison
    if (!_hasDataChanged(current, previous)) {
      return false;
    }

    // Additional significance checks can be added here
    // For now, we consider any data change as significant
    // Future enhancements could include:
    // - Checking if only minor fields changed (like last login time)
    // - Weighing different types of changes (education vs experience vs skills)
    // - Minimum change threshold (e.g., at least 3 fields changed)

    debugPrint('UserEmbeddingService: Significant data changes detected');
    return true;
  }

  /// Check only if user data has changed without triggering embedding update
  /// This method is specifically for profile screen navigation to avoid unnecessary API calls
  void checkDataChangesOnly(
    User user, {
    required VoidCallback onDataChanged,
    required VoidCallback onNoChanges,
  }) {
    try {
      debugPrint('UserEmbeddingService: ===== NAVIGATION DATA CHECK =====');
      debugPrint('UserEmbeddingService: User ID: ${user.id}');

      // Navigation debouncing check
      final now = DateTime.now();
      final userId = user.id ?? '';

      if (_lastNavigationUserId == userId &&
          _lastNavigationCheck != null &&
          now.difference(_lastNavigationCheck!).inSeconds <
              _navigationDebounceInterval.inSeconds) {
        debugPrint(
            'UserEmbeddingService: ⏰ NAVIGATION DEBOUNCE - Skipping check (${now.difference(_lastNavigationCheck!).inSeconds}s < ${_navigationDebounceInterval.inSeconds}s)');
        debugPrint(
            'UserEmbeddingService: ===== END NAVIGATION DATA CHECK =====');
        onNoChanges();
        return;
      }

      // Update navigation tracking
      _lastNavigationUserId = userId;
      _lastNavigationCheck = now;

      debugPrint(
          'UserEmbeddingService: Navigation debounce passed, checking data changes...');

      // Create current snapshot
      final currentSnapshot = UserDataComparator.createSnapshot(user);

      // Get previous snapshot
      final previousSnapshot = _storage.getUserDataSnapshot();

      // Check if data has changed
      bool hasChanged = _hasDataChanged(currentSnapshot, previousSnapshot);

      if (hasChanged) {
        debugPrint('UserEmbeddingService: Data changes detected in comparison');
        debugPrint(
            'UserEmbeddingService: ===== END NAVIGATION DATA CHECK =====');
        onDataChanged();
      } else {
        debugPrint(
            'UserEmbeddingService: No data changes detected in comparison');
        debugPrint(
            'UserEmbeddingService: ===== END NAVIGATION DATA CHECK =====');
        onNoChanges();
      }
    } catch (error) {
      debugPrint(
          'UserEmbeddingService: Error checking data changes: ${parseError(error)}');
      debugPrint('UserEmbeddingService: ===== END NAVIGATION DATA CHECK =====');
      // On error, assume no changes to be safe
      onNoChanges();
    }
  }

  /// Force update embedding regardless of data changes
  Future<void> forceUpdateEmbedding(
    User user, {
    VoidCallback? onSuccess,
    Function(String error)? onError,
  }) async {
    await checkAndUpdateEmbeddingOptimized(
      user,
      forceUpdate: true,
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Get the status of current embedding update task
  TaskStatus getEmbeddingTaskStatus() {
    return _taskManager.getTaskStatus(_embeddingTaskId);
  }

  /// Check if embedding update is currently running
  bool isEmbeddingUpdateRunning() {
    return _taskManager.isTaskRunning(_embeddingTaskId);
  }

  /// Cancel current embedding update task
  void cancelEmbeddingUpdate() {
    _taskManager.cancelTask(_embeddingTaskId);
  }

  /// Get last embedding update timestamp
  DateTime? getLastEmbeddingUpdate() {
    return _storage.getLastEmbeddingUpdate();
  }

  /// Get current user data snapshot
  UserDataSnapshot? getCurrentSnapshot() {
    return _storage.getUserDataSnapshot();
  }

  /// Clear all embedding data (useful for logout)
  Future<void> clearEmbeddingData() async {
    _taskManager.cancelTask(_embeddingTaskId);
    await _storage.clearUserEmbeddingData();

    // Clear debouncing data
    _lastProcessedUserId = null;
    _lastProcessedTime = null;
    _lastProcessedDataHash = null;
    _lastNavigationUserId = null;
    _lastNavigationCheck = null;

    debugPrint(
        'UserEmbeddingService: Cleared all embedding data and debouncing');
  }

  /// Clear debouncing data manually (useful for testing)
  void clearDebouncing() {
    _lastProcessedUserId = null;
    _lastProcessedTime = null;
    _lastProcessedDataHash = null;
    _lastNavigationUserId = null;
    _lastNavigationCheck = null;
    debugPrint('UserEmbeddingService: Cleared debouncing data');
  }

  /// Dispose of the service
  void dispose() {
    _taskManager.dispose();
  }
}
