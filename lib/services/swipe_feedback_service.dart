import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class SwipeFeedbackService {
  final Dio _dio;
  final String baseUrl;

  SwipeFeedbackService(this._dio, {required this.baseUrl});

  /// Gửi feedback cho một item (job/company)
  Future<Map<String, dynamic>> submitFeedback({
    required String itemId,
    required String itemType, // "job" hoặc "company"
    required int feedback, // 1 = like, -1 = dislike, 0 = neutral
    int? batchSize,
  }) async {
    try {
      final response = await _dio.post(
        '$baseUrl/swipe/feedback',
        data: {
          'item_id': itemId,
          'item_type': itemType,
          'feedback': feedback,
          if (batchSize != null) 'batch_size': batchSize,
        },
      );

      debugPrint('Feedback submitted successfully: ${response.data}');
      return response.data;
    } catch (e) {
      debugPrint('Error submitting feedback: $e');
      rethrow;
    }
  }

  /// Gửi nhiều feedback cùng lúc
  Future<Map<String, dynamic>> submitBulkFeedback({
    required List<Map<String, dynamic>> feedbacks,
    bool autoProcess = true,
  }) async {
    try {
      final response = await _dio.post(
        '$baseUrl/swipe/bulk-feedback',
        data: {
          'feedbacks': feedbacks,
          'auto_process': autoProcess,
        },
      );

      debugPrint('Bulk feedback submitted successfully: ${response.data}');
      return response.data;
    } catch (e) {
      debugPrint('Error submitting bulk feedback: $e');
      rethrow;
    }
  }

  /// Ép buộc xử lý batch feedback ngay lập tức
  Future<Map<String, dynamic>> forceBatchProcess() async {
    try {
      final response = await _dio.post('$baseUrl/swipe/batch-process');

      debugPrint('Batch process forced successfully: ${response.data}');
      return response.data;
    } catch (e) {
      debugPrint('Error forcing batch process: $e');
      rethrow;
    }
  }

  /// Lấy trạng thái feedback của người dùng
  Future<Map<String, dynamic>> getFeedbackStatus() async {
    try {
      final response = await _dio.get('$baseUrl/swipe/status');

      debugPrint('Feedback status retrieved: ${response.data}');
      return response.data;
    } catch (e) {
      debugPrint('Error getting feedback status: $e');
      rethrow;
    }
  }

  /// Xóa buffer feedback
  Future<Map<String, dynamic>> clearFeedbackBuffer() async {
    try {
      final response = await _dio.post('$baseUrl/swipe/clear-buffer');

      debugPrint('Feedback buffer cleared: ${response.data}');
      return response.data;
    } catch (e) {
      debugPrint('Error clearing feedback buffer: $e');
      rethrow;
    }
  }

  /// Helper method để gửi feedback like
  Future<Map<String, dynamic>> submitLike({
    required String itemId,
    required String itemType,
    int? batchSize,
  }) {
    return submitFeedback(
      itemId: itemId,
      itemType: itemType,
      feedback: 1,
      batchSize: batchSize,
    );
  }

  /// Helper method để gửi feedback dislike
  Future<Map<String, dynamic>> submitDislike({
    required String itemId,
    required String itemType,
    int? batchSize,
  }) {
    return submitFeedback(
      itemId: itemId,
      itemType: itemType,
      feedback: -1,
      batchSize: batchSize,
    );
  }

  /// Helper method để gửi feedback neutral
  Future<Map<String, dynamic>> submitNeutral({
    required String itemId,
    required String itemType,
    int? batchSize,
  }) {
    return submitFeedback(
      itemId: itemId,
      itemType: itemType,
      feedback: 0,
      batchSize: batchSize,
    );
  }
}
