import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/services/api_models/api_page_response/api_page_response.dart';
import 'package:pbl5/services/apis/api_client.dart';

class SearchRepository {
  final ApiClient apis;

  const SearchRepository({required this.apis});

  Future<ApiPageResponse<Company>> searchCompanies(
          {String query = '', int page = 1, int paging = 25}) =>
      apis.searchCompanies(query: query, page: page, paging: paging);
}
