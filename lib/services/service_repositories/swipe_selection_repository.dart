import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/models/pair/pair.dart';
import 'package:pbl5/services/apiAI/api_ai.dart';
import 'package:pbl5/services/api_models/api_page_response/api_page_response.dart';
import 'package:pbl5/services/api_models/api_response/api_response.dart';
import 'package:pbl5/services/apis/api_client.dart';

class SwipeSelectionRepository {
  final ApiClient apiClient;
  final ApiAI apiAI;

  const SwipeSelectionRepository(
      {required this.apiAI, required this.apiClient});

  Future<ApiPageResponse<Company>> getRecommendedCompanies(
          {int page = 1, int paging = 25}) =>
      apiAI.getRecommendedCompanies(page: page, paging: paging);

  Future<ApiResponse<Pair>> requestMatchedPair(String companyId) =>
      apiClient.requestMatchedPair(companyId);

  Future<ApiResponse<Pair>> acceptPair(String pairId) =>
      apiClient.acceptPair(pairId);

  Future<ApiResponse<Pair>> rejectPair(String pairId) =>
      apiClient.rejectPair(pairId);

  Future<ApiResponse<Pair>> getMatchById(String pairId) =>
      apiClient.getMatchById(pairId);

  Future<ApiResponse<Pair>> getMatchByAccountId(String accountId) =>
      apiClient.getMatchByAccountId(accountId);
}
