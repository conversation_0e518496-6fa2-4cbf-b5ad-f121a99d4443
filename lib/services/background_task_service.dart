import 'dart:async';
import 'package:flutter/foundation.dart';

/// Enum for task status
enum TaskStatus {
  idle,
  running,
  completed,
  failed,
  cancelled,
}

/// Background task manager for handling async operations
class BackgroundTaskManager {
  final Map<String, TaskInfo> _tasks = {};
  final Map<String, Timer> _timers = {};

  /// Execute a background task with retry logic
  Future<void> executeTask({
    required String taskId,
    required Future<void> Function() task,
    VoidCallback? onSuccess,
    Function(String error)? onError,
    Duration delay = const Duration(milliseconds: 500),
    bool enableRetry = true,
    int maxRetries = 3,
  }) async {
    // Cancel existing task if running
    cancelTask(taskId);

    // Create task info
    final taskInfo = TaskInfo(
      id: taskId,
      status: TaskStatus.idle,
      maxRetries: maxRetries,
      enableRetry: enableRetry,
    );
    _tasks[taskId] = taskInfo;

    debugPrint('BackgroundTaskManager: Scheduling task $taskId with delay ${delay.inMilliseconds}ms');

    // Schedule task execution with delay
    _timers[taskId] = Timer(delay, () async {
      await _executeTaskInternal(
        taskId: taskId,
        task: task,
        onSuccess: onSuccess,
        onError: onError,
      );
    });
  }

  /// Internal task execution with retry logic
  Future<void> _executeTaskInternal({
    required String taskId,
    required Future<void> Function() task,
    VoidCallback? onSuccess,
    Function(String error)? onError,
  }) async {
    final taskInfo = _tasks[taskId];
    if (taskInfo == null) return;

    try {
      taskInfo.status = TaskStatus.running;
      debugPrint('BackgroundTaskManager: Starting task $taskId (attempt ${taskInfo.currentRetry + 1}/${taskInfo.maxRetries})');

      await task();

      taskInfo.status = TaskStatus.completed;
      debugPrint('BackgroundTaskManager: Task $taskId completed successfully');
      onSuccess?.call();
      
      // Clean up
      _cleanupTask(taskId);
    } catch (error) {
      taskInfo.currentRetry++;
      final errorMessage = error.toString();
      
      debugPrint('BackgroundTaskManager: Task $taskId failed (attempt ${taskInfo.currentRetry}/${taskInfo.maxRetries}): $errorMessage');

      if (taskInfo.enableRetry && taskInfo.currentRetry < taskInfo.maxRetries) {
        // Retry with exponential backoff
        final retryDelay = Duration(seconds: taskInfo.currentRetry * 2);
        debugPrint('BackgroundTaskManager: Retrying task $taskId in ${retryDelay.inSeconds} seconds');
        
        _timers[taskId] = Timer(retryDelay, () async {
          await _executeTaskInternal(
            taskId: taskId,
            task: task,
            onSuccess: onSuccess,
            onError: onError,
          );
        });
      } else {
        taskInfo.status = TaskStatus.failed;
        debugPrint('BackgroundTaskManager: Task $taskId failed permanently after ${taskInfo.currentRetry} attempts');
        onError?.call(errorMessage);
        
        // Clean up
        _cleanupTask(taskId);
      }
    }
  }

  /// Cancel a running task
  void cancelTask(String taskId) {
    final timer = _timers[taskId];
    if (timer != null) {
      timer.cancel();
      _timers.remove(taskId);
      debugPrint('BackgroundTaskManager: Cancelled timer for task $taskId');
    }

    final taskInfo = _tasks[taskId];
    if (taskInfo != null) {
      taskInfo.status = TaskStatus.cancelled;
      debugPrint('BackgroundTaskManager: Cancelled task $taskId');
    }
    
    _cleanupTask(taskId);
  }

  /// Get task status
  TaskStatus getTaskStatus(String taskId) {
    return _tasks[taskId]?.status ?? TaskStatus.idle;
  }

  /// Check if task is running
  bool isTaskRunning(String taskId) {
    return getTaskStatus(taskId) == TaskStatus.running;
  }

  /// Clean up task resources
  void _cleanupTask(String taskId) {
    _timers.remove(taskId);
    _tasks.remove(taskId);
  }

  /// Cancel all tasks
  void cancelAllTasks() {
    final taskIds = _tasks.keys.toList();
    for (final taskId in taskIds) {
      cancelTask(taskId);
    }
    debugPrint('BackgroundTaskManager: Cancelled all tasks');
  }

  /// Get all task statuses
  Map<String, TaskStatus> getAllTaskStatuses() {
    return Map.fromEntries(
      _tasks.entries.map((entry) => MapEntry(entry.key, entry.value.status))
    );
  }

  /// Dispose of the task manager
  void dispose() {
    cancelAllTasks();
    debugPrint('BackgroundTaskManager: Disposed');
  }
}

/// Task information class
class TaskInfo {
  final String id;
  TaskStatus status;
  final int maxRetries;
  final bool enableRetry;
  int currentRetry;

  TaskInfo({
    required this.id,
    required this.status,
    required this.maxRetries,
    required this.enableRetry,
    this.currentRetry = 0,
  });
}
