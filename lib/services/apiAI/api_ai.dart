import 'package:dio/dio.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/services/api_models/api_page_response/api_page_response.dart';
import 'package:pbl5/services/api_models/api_response/api_response.dart';
import 'package:retrofit/http.dart';

part 'api_ai.g.dart';

@RestApi()
abstract class ApiAI {
  factory ApiAI(Dio dio, {String baseUrl}) = _ApiAI;

  @GET('/recommend/user')
  Future<ApiPageResponse<Company>> getRecommendedCompanies(
      {@Query('page') int page = 1, @Query('paging') int paging = 25});

  @POST('/embed/users')
  Future<ApiResponse> updateUserEmbedding(
      {@Query('X-API-KEY') required String apiKey,
      @Query('X-Account-ID') required String accountId});
}
