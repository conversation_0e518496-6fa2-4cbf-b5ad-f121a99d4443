import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:pbl5/models/user/user.dart';
import 'package:pbl5/models/user_embedding_data/user_embedding_models.dart';

/// Optimized user data comparator with stable hashing and deep comparison
class UserDataComparator {
  /// Generate a stable hash of user data for comparison
  /// Uses simple string concatenation for deterministic results
  static String generateUserDataHash(User user) {
    debugPrint('UserDataComparator: ===== GENERATING HASH =====');

    // Build a deterministic string representation of core user data
    final buffer = StringBuffer();

    // Basic profile data (order matters for consistency)
    buffer.write('PROFILE:');
    buffer.write('firstName=${user.firstName ?? ""}|');
    buffer.write('lastName=${user.lastName ?? ""}|');
    buffer.write('email=${user.email ?? ""}|');
    buffer.write('phone=${user.phoneNumber ?? ""}|');
    buffer.write('address=${user.address ?? ""}|');
    buffer.write('summary=${user.summaryIntroduction ?? ""}|');

    // Education data (sorted for consistency)
    buffer.write('EDUCATION:');
    final educations = user.educations.toList();
    educations.sort((a, b) => '${a.studyPlace ?? ""}_${a.majority ?? ""}'
        .compareTo('${b.studyPlace ?? ""}_${b.majority ?? ""}'));
    for (final edu in educations) {
      buffer.write(
          '${edu.studyPlace ?? ""}|${edu.majority ?? ""}|${edu.studyStartTime ?? ""}|${edu.studyEndTime ?? ""}|${edu.cpa ?? 0.0}|${edu.note ?? ""}|${edu.isUniversity ?? false}||');
    }

    // Experience data (sorted for consistency)
    buffer.write('EXPERIENCE:');
    final experiences = user.experiences.toList();
    experiences.sort((a, b) => '${a.workPlace ?? ""}_${a.position ?? ""}'
        .compareTo('${b.workPlace ?? ""}_${b.position ?? ""}'));
    for (final exp in experiences) {
      buffer.write(
          '${exp.workPlace ?? ""}|${exp.position ?? ""}|${exp.experienceTitle ?? ""}|${exp.experienceStartTime ?? ""}|${exp.experienceEndTime ?? ""}|${exp.note ?? ""}||');
    }

    // Language data (sorted for consistency)
    buffer.write('LANGUAGES:');
    final languages = user.languages.toList();
    languages
        .sort((a, b) => (a.languageName ?? "").compareTo(b.languageName ?? ""));
    for (final lang in languages) {
      buffer.write('${lang.languageName ?? ""}|${lang.languageScore ?? ""}||');
    }

    // Social media links (sorted for consistency)
    buffer.write('SOCIAL:');
    final socialLinks = user.socialMediaLink.toList();
    socialLinks.sort();
    for (final link in socialLinks) {
      buffer.write('$link|');
    }

    final dataString = buffer.toString();
    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    final hash = digest.toString();

    debugPrint('UserDataComparator: Generated hash: $hash');
    debugPrint('UserDataComparator: ===== END GENERATING HASH =====');

    return hash;
  }

  /// Create a snapshot of user data for comparison
  static UserDataSnapshot createSnapshot(User user) {
    final dataHash = generateUserDataHash(user);

    return UserDataSnapshot(
      firstName: user.firstName ?? '',
      lastName: user.lastName ?? '',
      summaryIntroduction: user.summaryIntroduction ?? '',
      address: user.address ?? '',
      phoneNumber: user.phoneNumber ?? '',
      email: user.email ?? '',
      educations: user.educations
          .map((e) => {
                'studyPlace': e.studyPlace ?? '',
                'majority': e.majority ?? '',
                'studyStartTime': e.studyStartTime ?? '',
                'studyEndTime': e.studyEndTime ?? '',
                'cpa': e.cpa ?? 0.0,
                'note': e.note ?? '',
                'isUniversity': e.isUniversity ?? false,
              })
          .toList(),
      experiences: user.experiences
          .map((e) => {
                'workPlace': e.workPlace ?? '',
                'position': e.position ?? '',
                'experienceTitle': e.experienceTitle ?? '',
                'experienceStartTime': e.experienceStartTime ?? '',
                'experienceEndTime': e.experienceEndTime ?? '',
                'note': e.note ?? '',
              })
          .toList(),
      languages: user.languages
          .map((l) => {
                'languageName': l.languageName ?? '',
                'languageScore': l.languageScore ?? '',
                'languageCertificateDate': l.languageCertificateDate ?? '',
              })
          .toList(),
      applicationPositions: user.applicationPositions
          .map((p) => {
                'applyPositionTitle': p.applyPositionTitle ?? '',
                'salary': p.salary ?? '',
              })
          .toList(),
      socialMediaLink: user.socialMediaLink ?? [],
      // Use actual timestamp only when data changes, not for comparison
      lastUpdated: DateTime.now().toIso8601String(),
      dataHash: dataHash,
    );
  }

  /// Compare two user data snapshots
  static bool hasDataChanged(UserDataSnapshot? previous, User current) {
    if (previous == null) return true;

    final currentHash = generateUserDataHash(current);
    final hasChanged = previous.dataHash != currentHash;

    if (hasChanged) {
      debugPrint(
          'User data changed detected. Previous hash: ${previous.dataHash}, Current hash: $currentHash');
    }

    return hasChanged;
  }

  /// Generate structured text for embedding API
  static String generateStructuredText(User user) {
    final buffer = StringBuffer();

    // Basic info
    buffer.writeln('Name: ${user.firstName ?? ''} ${user.lastName ?? ''}');
    buffer.writeln('Location: ${user.address ?? ''}');
    buffer.writeln('Summary: ${user.summaryIntroduction ?? ''}');
    buffer.writeln();

    // Education
    buffer.writeln('Education:');
    for (final education in user.educations) {
      buffer.writeln(
          '- ${education.studyPlace ?? ''}, ${education.majority ?? ''} (${education.studyStartTime ?? ''}-${education.studyEndTime ?? ''})');
    }
    buffer.writeln();

    // Experience
    buffer.writeln('Experience:');
    for (final experience in user.experiences) {
      buffer.writeln(
          '- ${experience.position ?? ''} at ${experience.workPlace ?? ''} (${experience.experienceStartTime ?? ''}-${experience.experienceEndTime ?? ''})');
      if (experience.note?.isNotEmpty == true) {
        buffer.writeln('  ${experience.note}');
      }
    }
    buffer.writeln();

    // Languages
    if (user.languages.isNotEmpty) {
      buffer.write('Languages: ');
      buffer.writeln(user.languages.map((l) => l.languageName).join(', '));
    }

    return buffer.toString();
  }

  /// Create embedding data payload for API
  static UserEmbeddingData createEmbeddingData(User user) {
    final fullName = '${user.firstName ?? ''} ${user.lastName ?? ''}'.trim();
    final description = user.summaryIntroduction ?? '';
    final structuredText = generateStructuredText(user);

    return UserEmbeddingData(
      accountId: user.id ?? '',
      description: description,
      name: fullName,
      structuredText: structuredText,
    );
  }
}
