/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';

class $AssetsBackgroundsGen {
  const $AssetsBackgroundsGen();

  /// File path: assets/Backgrounds/Spline.png
  AssetGenImage get spline =>
      const AssetGenImage('assets/Backgrounds/Spline.png');

  /// List of all assets
  List<AssetGenImage> get values => [spline];
}

class $AssetsRiveAssetsGen {
  const $AssetsRiveAssetsGen();

  /// File path: assets/RiveAssets/button.riv
  String get button => 'assets/RiveAssets/button.riv';

  /// File path: assets/RiveAssets/check.riv
  String get check => 'assets/RiveAssets/check.riv';

  /// File path: assets/RiveAssets/confetti.riv
  String get confetti => 'assets/RiveAssets/confetti.riv';

  /// File path: assets/RiveAssets/house.riv
  String get house => 'assets/RiveAssets/house.riv';

  /// File path: assets/RiveAssets/icons.riv
  String get icons => 'assets/RiveAssets/icons.riv';

  /// File path: assets/RiveAssets/menu_button.riv
  String get menuButton => 'assets/RiveAssets/menu_button.riv';

  /// File path: assets/RiveAssets/shapes.riv
  String get shapes => 'assets/RiveAssets/shapes.riv';

  /// List of all assets
  List<String> get values =>
      [button, check, confetti, house, icons, menuButton, shapes];
}

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Inter-Regular.ttf
  String get interRegular => 'assets/fonts/Inter-Regular.ttf';

  /// File path: assets/fonts/Inter-SemiBold.ttf
  String get interSemiBold => 'assets/fonts/Inter-SemiBold.ttf';

  /// File path: assets/fonts/NotoSans-Black.ttf
  String get notoSansBlack => 'assets/fonts/NotoSans-Black.ttf';

  /// File path: assets/fonts/NotoSans-BlackItalic.ttf
  String get notoSansBlackItalic => 'assets/fonts/NotoSans-BlackItalic.ttf';

  /// File path: assets/fonts/NotoSans-Bold.ttf
  String get notoSansBold => 'assets/fonts/NotoSans-Bold.ttf';

  /// File path: assets/fonts/NotoSans-BoldItalic.ttf
  String get notoSansBoldItalic => 'assets/fonts/NotoSans-BoldItalic.ttf';

  /// File path: assets/fonts/NotoSans-ExtraBold.ttf
  String get notoSansExtraBold => 'assets/fonts/NotoSans-ExtraBold.ttf';

  /// File path: assets/fonts/NotoSans-ExtraBoldItalic.ttf
  String get notoSansExtraBoldItalic =>
      'assets/fonts/NotoSans-ExtraBoldItalic.ttf';

  /// File path: assets/fonts/NotoSans-ExtraLight.ttf
  String get notoSansExtraLight => 'assets/fonts/NotoSans-ExtraLight.ttf';

  /// File path: assets/fonts/NotoSans-ExtraLightItalic.ttf
  String get notoSansExtraLightItalic =>
      'assets/fonts/NotoSans-ExtraLightItalic.ttf';

  /// File path: assets/fonts/NotoSans-Italic.ttf
  String get notoSansItalic => 'assets/fonts/NotoSans-Italic.ttf';

  /// File path: assets/fonts/NotoSans-Light.ttf
  String get notoSansLight => 'assets/fonts/NotoSans-Light.ttf';

  /// File path: assets/fonts/NotoSans-LightItalic.ttf
  String get notoSansLightItalic => 'assets/fonts/NotoSans-LightItalic.ttf';

  /// File path: assets/fonts/NotoSans-Medium.ttf
  String get notoSansMedium => 'assets/fonts/NotoSans-Medium.ttf';

  /// File path: assets/fonts/NotoSans-MediumItalic.ttf
  String get notoSansMediumItalic => 'assets/fonts/NotoSans-MediumItalic.ttf';

  /// File path: assets/fonts/NotoSans-Regular.ttf
  String get notoSansRegular => 'assets/fonts/NotoSans-Regular.ttf';

  /// File path: assets/fonts/NotoSans-SemiBold.ttf
  String get notoSansSemiBold => 'assets/fonts/NotoSans-SemiBold.ttf';

  /// File path: assets/fonts/NotoSans-SemiBoldItalic.ttf
  String get notoSansSemiBoldItalic =>
      'assets/fonts/NotoSans-SemiBoldItalic.ttf';

  /// File path: assets/fonts/NotoSans-Thin.ttf
  String get notoSansThin => 'assets/fonts/NotoSans-Thin.ttf';

  /// File path: assets/fonts/NotoSans-ThinItalic.ttf
  String get notoSansThinItalic => 'assets/fonts/NotoSans-ThinItalic.ttf';

  /// File path: assets/fonts/Poppins-Bold.ttf
  String get poppinsBold => 'assets/fonts/Poppins-Bold.ttf';

  /// List of all assets
  List<String> get values => [
        interRegular,
        interSemiBold,
        notoSansBlack,
        notoSansBlackItalic,
        notoSansBold,
        notoSansBoldItalic,
        notoSansExtraBold,
        notoSansExtraBoldItalic,
        notoSansExtraLight,
        notoSansExtraLightItalic,
        notoSansItalic,
        notoSansLight,
        notoSansLightItalic,
        notoSansMedium,
        notoSansMediumItalic,
        notoSansRegular,
        notoSansSemiBold,
        notoSansSemiBoldItalic,
        notoSansThin,
        notoSansThinItalic,
        poppinsBold
      ];
}

class $AssetsHtmlsGen {
  const $AssetsHtmlsGen();

  /// File path: assets/htmls/webview_layout.html
  String get webviewLayout => 'assets/htmls/webview_layout.html';

  /// List of all assets
  List<String> get values => [webviewLayout];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/Arrow Right.svg
  SvgGenImage get arrowRight =>
      const SvgGenImage('assets/icons/Arrow Right.svg');

  /// File path: assets/icons/User.svg
  SvgGenImage get user => const SvgGenImage('assets/icons/User.svg');

  /// File path: assets/icons/apple_box.svg
  SvgGenImage get appleBox => const SvgGenImage('assets/icons/apple_box.svg');

  /// File path: assets/icons/code.svg
  SvgGenImage get code => const SvgGenImage('assets/icons/code.svg');

  /// File path: assets/icons/email.svg
  SvgGenImage get email => const SvgGenImage('assets/icons/email.svg');

  /// File path: assets/icons/email_box.svg
  SvgGenImage get emailBox => const SvgGenImage('assets/icons/email_box.svg');

  /// File path: assets/icons/google_box.svg
  SvgGenImage get googleBox => const SvgGenImage('assets/icons/google_box.svg');

  /// File path: assets/icons/ic_arrow_right_in_chat.svg
  SvgGenImage get icArrowRightInChat =>
      const SvgGenImage('assets/icons/ic_arrow_right_in_chat.svg');

  /// File path: assets/icons/ic_attach_file_in_chat.svg
  SvgGenImage get icAttachFileInChat =>
      const SvgGenImage('assets/icons/ic_attach_file_in_chat.svg');

  /// File path: assets/icons/ic_avatar.svg
  SvgGenImage get icAvatar => const SvgGenImage('assets/icons/ic_avatar.svg');

  /// File path: assets/icons/ic_camera_in_chat.svg
  SvgGenImage get icCameraInChat =>
      const SvgGenImage('assets/icons/ic_camera_in_chat.svg');

  /// File path: assets/icons/ic_empty_conversation.svg
  SvgGenImage get icEmptyConversation =>
      const SvgGenImage('assets/icons/ic_empty_conversation.svg');

  /// File path: assets/icons/ic_empty_message.svg
  SvgGenImage get icEmptyMessage =>
      const SvgGenImage('assets/icons/ic_empty_message.svg');

  /// File path: assets/icons/ic_empty_notification.svg
  SvgGenImage get icEmptyNotification =>
      const SvgGenImage('assets/icons/ic_empty_notification.svg');

  /// File path: assets/icons/ic_error_dialog.svg
  SvgGenImage get icErrorDialog =>
      const SvgGenImage('assets/icons/ic_error_dialog.svg');

  /// File path: assets/icons/ic_error_text_field.svg
  SvgGenImage get icErrorTextField =>
      const SvgGenImage('assets/icons/ic_error_text_field.svg');

  /// File path: assets/icons/ic_eye.svg
  SvgGenImage get icEye => const SvgGenImage('assets/icons/ic_eye.svg');

  /// File path: assets/icons/ic_eye_slash.svg
  SvgGenImage get icEyeSlash =>
      const SvgGenImage('assets/icons/ic_eye_slash.svg');

  /// File path: assets/icons/ic_file_message.svg
  SvgGenImage get icFileMessage =>
      const SvgGenImage('assets/icons/ic_file_message.svg');

  /// File path: assets/icons/ic_gallery_in_chat.svg
  SvgGenImage get icGalleryInChat =>
      const SvgGenImage('assets/icons/ic_gallery_in_chat.svg');

  /// File path: assets/icons/ic_key.svg
  SvgGenImage get icKey => const SvgGenImage('assets/icons/ic_key.svg');

  /// File path: assets/icons/ic_send_message.svg
  SvgGenImage get icSendMessage =>
      const SvgGenImage('assets/icons/ic_send_message.svg');

  /// File path: assets/icons/ic_success_dialog.svg
  SvgGenImage get icSuccessDialog =>
      const SvgGenImage('assets/icons/ic_success_dialog.svg');

  /// File path: assets/icons/ic_x.svg
  SvgGenImage get icX => const SvgGenImage('assets/icons/ic_x.svg');

  /// File path: assets/icons/ios.svg
  SvgGenImage get ios => const SvgGenImage('assets/icons/ios.svg');

  /// File path: assets/icons/password.svg
  SvgGenImage get password => const SvgGenImage('assets/icons/password.svg');

  /// File path: assets/icons/profile_img.png
  AssetGenImage get profileImg =>
      const AssetGenImage('assets/icons/profile_img.png');

  /// List of all assets
  List<dynamic> get values => [
        arrowRight,
        user,
        appleBox,
        code,
        email,
        emailBox,
        googleBox,
        icArrowRightInChat,
        icAttachFileInChat,
        icAvatar,
        icCameraInChat,
        icEmptyConversation,
        icEmptyMessage,
        icEmptyNotification,
        icErrorDialog,
        icErrorTextField,
        icEye,
        icEyeSlash,
        icFileMessage,
        icGalleryInChat,
        icKey,
        icSendMessage,
        icSuccessDialog,
        icX,
        ios,
        password,
        profileImg
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/avatar_placeholder.png
  AssetGenImage get avatarPlaceholder =>
      const AssetGenImage('assets/images/avatar_placeholder.png');

  /// File path: assets/images/company1.jpeg
  AssetGenImage get company1 =>
      const AssetGenImage('assets/images/company1.jpeg');

  /// File path: assets/images/company2.png
  AssetGenImage get company2 =>
      const AssetGenImage('assets/images/company2.png');

  /// File path: assets/images/company3.jpeg
  AssetGenImage get company3 =>
      const AssetGenImage('assets/images/company3.jpeg');

  /// File path: assets/images/company4.png
  AssetGenImage get company4 =>
      const AssetGenImage('assets/images/company4.png');

  /// File path: assets/images/company5.png
  AssetGenImage get company5 =>
      const AssetGenImage('assets/images/company5.png');

  /// File path: assets/images/company_placeholder.png
  AssetGenImage get companyPlaceholder =>
      const AssetGenImage('assets/images/company_placeholder.png');

  /// File path: assets/images/dash_404_not_found.png
  AssetGenImage get dash404NotFound =>
      const AssetGenImage('assets/images/dash_404_not_found.png');

  /// File path: assets/images/error_background.png
  AssetGenImage get errorBackground =>
      const AssetGenImage('assets/images/error_background.png');

  /// File path: assets/images/female_ava.jpg
  AssetGenImage get femaleAva =>
      const AssetGenImage('assets/images/female_ava.jpg');

  /// File path: assets/images/image_loading.png
  AssetGenImage get imageLoading =>
      const AssetGenImage('assets/images/image_loading.png');

  /// File path: assets/images/login.svg
  SvgGenImage get login => const SvgGenImage('assets/images/login.svg');

  /// File path: assets/images/logo_dash.png
  AssetGenImage get logoDash =>
      const AssetGenImage('assets/images/logo_dash.png');

  /// File path: assets/images/male_ava.jpg
  AssetGenImage get maleAva =>
      const AssetGenImage('assets/images/male_ava.jpg');

  /// File path: assets/images/register.svg
  SvgGenImage get register => const SvgGenImage('assets/images/register.svg');

  /// File path: assets/images/video_thumbnail.png
  AssetGenImage get videoThumbnail =>
      const AssetGenImage('assets/images/video_thumbnail.png');

  /// List of all assets
  List<dynamic> get values => [
        avatarPlaceholder,
        company1,
        company2,
        company3,
        company4,
        company5,
        companyPlaceholder,
        dash404NotFound,
        errorBackground,
        femaleAva,
        imageLoading,
        login,
        logoDash,
        maleAva,
        register,
        videoThumbnail
      ];
}

class $AssetsLottiesGen {
  const $AssetsLottiesGen();

  /// File path: assets/lotties/ic_pin.json
  LottieGenImage get icPin =>
      const LottieGenImage('assets/lotties/ic_pin.json');

  /// List of all assets
  List<LottieGenImage> get values => [icPin];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// File path: assets/translations/vi.json
  String get vi => 'assets/translations/vi.json';

  /// List of all assets
  List<String> get values => [en, vi];
}

class Assets {
  Assets._();

  static const $AssetsBackgroundsGen backgrounds = $AssetsBackgroundsGen();
  static const $AssetsRiveAssetsGen riveAssets = $AssetsRiveAssetsGen();
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsHtmlsGen htmls = $AssetsHtmlsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottiesGen lotties = $AssetsLottiesGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider() => AssetImage(_assetName);

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName);

  final String _assetName;

  SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    SvgTheme theme = const SvgTheme(),
    ColorFilter? colorFilter,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated Clip? clipBehavior,
    @deprecated bool cacheColorFilter = false,
  }) {
    return SvgPicture.asset(
      _assetName,
      key: key,
      matchTextDirection: matchTextDirection,
      bundle: bundle,
      package: package,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      theme: theme,
      colorFilter: colorFilter,
      color: color,
      colorBlendMode: colorBlendMode,
      clipBehavior: clipBehavior ?? Clip.none,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName);

  final String _assetName;

  LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    LottieDelegates? delegates,
    LottieOptions? options,
    void Function(LottieComposition)? onLoaded,
    LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, LottieComposition?)? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
  }) {
    return Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
