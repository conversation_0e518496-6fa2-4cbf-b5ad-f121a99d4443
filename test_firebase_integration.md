# Firebase Integration Test Guide

## Kiểm tra Firebase Integration cho Job Swipe App

### 1. Kiểm tra Firebase Authentication
```bash
# Đăng nhập và kiểm tra Firebase token
# Trong log sẽ thấy:
# - "Firebase authentication successful"
# - "FCM Token: [token]"
# - "Firebase Auth successful for user: [userId]"
```

### 2. Kiểm tra FCM Notifications

#### Test Match Notification:
```bash
# Khi user swipe right và tạo match:
# 1. Gọi API requestMatchedPair
# 2. Server sẽ gửi FCM notification với type "new_match"
# 3. Mobile app nhận notification và hiển thị
# 4. Tap notification sẽ navigate đến company detail
```

#### Test Message Notification:
```bash
# Khi gửi message trong chat:
# 1. Message được gửi qua API truyền thống
# 2. Đồng thời gửi qua Firebase Realtime Database
# 3. Server gửi FCM notification với type "new_message"
# 4. Người nhận thấy notification realtime
```

### 3. Kiểm tra Firebase Realtime Database

#### Test Chat Realtime:
```bash
# Trong chat screen:
# 1. Mở conversation → tự động setup Firebase listener
# 2. Gửi message → message xuất hiện realtime ở cả 2 phía
# 3. Đóng chat → cleanup Firebase subscription
```

### 4. Test Commands

#### Test FCM Token Registration:
```bash
curl -X POST http://localhost:8080/api/v1/firebase/register-fcm-token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [access_token]" \
  -d '{"fcm_token": "test_token_123"}'
```

#### Test Match Notification:
```bash
curl -X POST http://localhost:8080/api/v1/firebase/match/send-match-notification \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [access_token]" \
  -d '{
    "recipient_id": "user_id",
    "matched_with_name": "Company ABC",
    "match_id": "match_123",
    "fcm_token": "fcm_token_123"
  }'
```

#### Test Message Notification:
```bash
curl -X POST http://localhost:8080/api/v1/firebase/chat/send-message-notification \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [access_token]" \
  -d '{
    "recipient_id": "user_id",
    "sender_name": "John Doe",
    "message_content": "Hello there!",
    "conversation_id": "conv_123",
    "fcm_token": "fcm_token_123"
  }'
```

### 5. Debug Logs để kiểm tra

#### Firebase Service Logs:
```
✅ Firebase Auth successful for user: [userId]
✅ FCM Token: [token]
✅ FCM Token registered successfully
✅ Chat room created: [conversationId]
✅ Message sent successfully: [messageId]
✅ Firebase messages stream listening...
```

#### Notification Logs:
```
✅ Received foreground message: [messageId]
✅ Navigate to: new_match with data: {match_id: xxx}
✅ Navigate to: new_message with data: {conversation_id: xxx}
```

### 6. Flow Test hoàn chỉnh

#### Match Flow:
1. User A swipe right company B
2. Server tạo match record
3. Server gửi FCM notification đến User A
4. User A nhận notification "New Match!"
5. Tap notification → navigate to company detail

#### Chat Flow:
1. User A và Company B đã match
2. User A mở chat với Company B
3. Firebase listener được setup
4. User A gửi message "Hello"
5. Message gửi qua API + Firebase
6. Company B nhận FCM notification
7. Company B mở app → thấy message realtime

### 7. Troubleshooting

#### Nếu không nhận được notification:
- Kiểm tra FCM token đã register chưa
- Kiểm tra app có ở foreground/background
- Kiểm tra notification permissions
- Kiểm tra Firebase project config

#### Nếu realtime chat không hoạt động:
- Kiểm tra Firebase Database rules
- Kiểm tra network connection
- Kiểm tra Firebase Auth status
- Kiểm tra subscription cleanup

### 8. Production Checklist

- [ ] Firebase project đã setup đúng
- [ ] FCM keys đã config trong server
- [ ] Database rules đã setup security
- [ ] Notification channels đã tạo
- [ ] Navigation routes đã implement
- [ ] Error handling đã complete
- [ ] Performance optimization đã làm
