# <PERSON><PERSON><PERSON> curl để test Firebase API
# 1. Login với User
curl -X POST http://localhost:8080/v1/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>", "password":"password123"}'

# 2. Login với Company
curl -X POST http://localhost:8080/v1/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>", "password":"password123"}'

# 3. Cập nhật FCM token cho User (thay YOUR_USER_ACCESS_TOKEN bằng token nhận được sau khi login)
curl -X POST http://localhost:8080/v1/firebase/fcm-token -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"fcmToken":"user-fcm-token-123"}'

# 4. <PERSON><PERSON><PERSON> nhật FCM token cho Company (thay YOUR_COMPANY_ACCESS_TOKEN bằng token nhận được sau khi login)
curl -X POST http://localhost:8080/v1/firebase/fcm-token -H "Authorization: Bearer YOUR_COMPANY_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"fcmToken":"company-fcm-token-456"}'

# 5. Gửi thông báo test cho User
curl -X POST http://localhost:8080/v1/firebase/test-notification -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN"

# 6. Gửi thông báo test cho Company
curl -X POST http://localhost:8080/v1/firebase/test-notification -H "Authorization: Bearer YOUR_COMPANY_ACCESS_TOKEN"

# 7. Lấy danh sách cuộc trò chuyện của User
curl -X GET http://localhost:8080/v1/chats/conversations -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN"

# 8. Gửi tin nhắn từ User đến Company (thay CONVERSATION_ID bằng ID cuộc trò chuyện)
curl -X POST http://localhost:8080/v1/chats/conversations/CONVERSATION_ID/messages -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"content":"Xin chào từ user 94683ac4-12e7-43cc-8a44-f9d5fb9f77b1"}'

# 9. Gửi tin nhắn từ Company đến User (thay CONVERSATION_ID bằng ID cuộc trò chuyện)
curl -X POST http://localhost:8080/v1/chats/conversations/CONVERSATION_ID/messages -H "Authorization: Bearer YOUR_COMPANY_ACCESS_TOKEN" -H "Content-Type: application/json" -d '{"content":"Xin chào từ company 0853a625-6b9a-47c4-a435-b7c7412f7efc"}'

# LƯU Ý: Thay các giá trị YOUR_USER_ACCESS_TOKEN, YOUR_COMPANY_ACCESS_TOKEN và CONVERSATION_ID bằng giá trị thực tế.
