package org.dhv.pbl5server.profile_service.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.dhv.pbl5server.common_service.model.AbstractEntity;
import java.util.UUID;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Entity
@Table(name = "application_skills")
public class ApplicationSkill extends AbstractEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue
	private UUID id;
	
	@Column(name = "description")
	private String note;
	
	@Column(name = "skill_name", length = 1000)
	private String skillName;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "application_position_id")
	private ApplicationPosition applicationPosition;
}
