package org.dhv.pbl5server.profile_service.mapper;

import org.dhv.pbl5server.common_service.config.SpringMapStructConfig;
import org.dhv.pbl5server.constant_service.mapper.ConstantMapper;
import org.dhv.pbl5server.profile_service.entity.ApplicationPosition;
import org.dhv.pbl5server.profile_service.payload.request.ApplicationPositionRequest;
import org.dhv.pbl5server.profile_service.payload.request.ApplicationSkillRequest;
import org.dhv.pbl5server.profile_service.payload.response.ApplicationPositionResponse;
import org.dhv.pbl5server.profile_service.payload.response.ApplicationPositionWithCompanyInfoResponse;
import org.dhv.pbl5server.profile_service.payload.response.CompanyProfileResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = SpringMapStructConfig.class, uses = { ConstantMapper.class, ApplicationSkillMapper.class })
public interface ApplicationPositionMapper {
    public static final String NAMED_ToApplicationPositionResponse = "toApplicationPositionResponse";

    @Mapping(source = "skills", target = "skills")
    @Mapping(source = "applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "salary", target = "salary")
    ApplicationPosition toApplicationPosition(ApplicationPositionRequest request);

    @Mapping(source = "request.id", target = "id")
    @Mapping(source = "request.status", target = "status")
    @Mapping(source = "request.skills", target = "skills")
    @Mapping(source = "request.applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "request.salary", target = "salary")
    ApplicationPosition toApplicationPosition(ApplicationPosition applicationPosition,
            ApplicationPositionRequest request);

    @Mapping(source = "request", target = "skills")
    @Mapping(source = "applicationPosition.applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "applicationPosition.salary", target = "salary")
    ApplicationPosition toApplicationPosition(ApplicationPosition applicationPosition,
            List<ApplicationSkillRequest> request);

    @Named(NAMED_ToApplicationPositionResponse)
    @Mapping(source = "skills", target = "skills")
    @Mapping(source = "applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "salary", target = "salary")
    ApplicationPositionResponse toApplicationPositionResponse(ApplicationPosition applicationPosition);

    @Mapping(source = "skills", target = "skills")
    @Mapping(source = "applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "salary", target = "salary")
    ApplicationPositionResponse toApplicationPositionResponseWithBasicInfoOnly(ApplicationPosition applicationPosition);

    @Mapping(source = "applicationPosition.skills", target = "skills")
    @Mapping(source = "applicationPosition.applyPositionTitle", target = "applyPositionTitle")
    @Mapping(source = "applicationPosition.salary", target = "salary")
    @Mapping(source = "applicationPosition.status", target = "status")
    @Mapping(source = "applicationPosition.id", target = "id")
    @Mapping(source = "applicationPosition.createdAt", target = "createdAt")
    @Mapping(source = "applicationPosition.updatedAt", target = "updatedAt")
    @Mapping(source = "companyProfileResponse", target = "companyProfile")
    ApplicationPositionWithCompanyInfoResponse toApplicationPositionWithCompanyInfoResponse(
            final ApplicationPosition applicationPosition, final CompanyProfileResponse companyProfileResponse);
}
