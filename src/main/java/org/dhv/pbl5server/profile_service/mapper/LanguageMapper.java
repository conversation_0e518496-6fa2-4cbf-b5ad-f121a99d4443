package org.dhv.pbl5server.profile_service.mapper;

import org.dhv.pbl5server.common_service.config.SpringMapStructConfig;
import org.dhv.pbl5server.constant_service.mapper.ConstantMapper;
import org.dhv.pbl5server.profile_service.entity.Language;
import org.dhv.pbl5server.profile_service.payload.request.LanguageRequest;
import org.dhv.pbl5server.profile_service.payload.response.LanguageResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(config = SpringMapStructConfig.class, uses = {ConstantMapper.class})
public interface LanguageMapper {
    public static final String NAMED_ToLanguageResponse = "toLanguageResponse";

    @Mapping(source = "languageScore", target = "languageScore")
    @Mapping(source = "languageCertificateDate", target = "languageCertificateDate")
    Language toLanguage(LanguageRequest request);

    @Mapping(source = "request.id", target = "id")
    @Mapping(source = "request.languageName", target = "languageName")
    @Mapping(source = "request.languageScore", target = "languageScore")
    @Mapping(source = "request.languageCertificateDate", target = "languageCertificateDate")
    @Mapping(source = "request.languageCertificateName", target = "languageCertificateName")
    Language toLanguage(Language language, LanguageRequest request);

    @Named(NAMED_ToLanguageResponse)
    @Mapping(source = "languageScore", target = "languageScore")
    @Mapping(source = "languageCertificateDate", target = "languageCertificateDate")
    LanguageResponse toLanguageResponse(Language language);
}
