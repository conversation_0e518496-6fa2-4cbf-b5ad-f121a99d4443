package org.dhv.pbl5server.profile_service.mapper;

import org.dhv.pbl5server.common_service.config.SpringMapStructConfig;
import org.dhv.pbl5server.constant_service.mapper.ConstantMapper;
import org.dhv.pbl5server.profile_service.entity.ApplicationSkill;
import org.dhv.pbl5server.profile_service.payload.request.ApplicationSkillRequest;
import org.dhv.pbl5server.profile_service.payload.response.ApplicationSkillResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = SpringMapStructConfig.class, uses = {ConstantMapper.class})
public interface ApplicationSkillMapper {
    @Mapping(source = "skillName", target = "skillName")
    ApplicationSkill toApplicationSkill(ApplicationSkillRequest request);

    @Mapping(source = "request.id", target = "id")
    @Mapping(source = "request.skillName", target = "skillName")
    @Mapping(source = "request.note", target = "note")
    ApplicationSkill toApplicationSkill(ApplicationSkill applicationSkill, ApplicationSkillRequest request);

    @Mapping(source = "skillName", target = "skillName")
    ApplicationSkillResponse toApplicationSkillResponse(ApplicationSkill applicationSkill);
}
