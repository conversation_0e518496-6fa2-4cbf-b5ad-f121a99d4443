package org.dhv.pbl5server.profile_service.payload.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonSnakeCaseNaming
public class ApplicationSkillResponse {
    private UUID id;
    
    @JsonProperty(value = "description")
    private String note;
    private String skillName;
    private String createdAt;
    private String updatedAt;
}
