package org.dhv.pbl5server.profile_service.payload.response;

import java.util.List;
import java.util.UUID;

import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonSnakeCaseNaming
@Builder
public class ApplicationPositionWithCompanyInfoResponse {

	private UUID id;
	private Boolean status;
	private String applyPositionTitle;

	@JsonProperty(value = "description")
	private String note;
	private String salary;
	private List<ApplicationSkillResponse> skills;
	private String createdAt;
	private String updatedAt;
	
	private CompanyProfileResponse companyProfile;
}
