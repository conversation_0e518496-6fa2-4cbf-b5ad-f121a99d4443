package org.dhv.pbl5server.profile_service.payload.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonSnakeCaseNaming
public class ApplicationPositionResponse {
    private UUID id;
    private Boolean status;
    private String applyPositionTitle;

    @JsonProperty(value = "description")
    private String note;
    private String salary;
    private List<ApplicationSkillResponse> skills;
    private String createdAt;
    private String updatedAt;
}
