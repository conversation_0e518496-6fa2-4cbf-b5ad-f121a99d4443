package org.dhv.pbl5server.profile_service.repository;

import org.dhv.pbl5server.profile_service.entity.CompanyExperience;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyExperienceRepository extends JpaRepository<CompanyExperience, String> {

    @Query("SELECT ce FROM CompanyExperience ce WHERE ce.company.id = :companyId")
    List<CompanyExperience> findByCompanyId(@Param("companyId") String companyId);

    @Query("DELETE FROM CompanyExperience ce WHERE ce.company.id = :companyId")
    void deleteByCompanyId(@Param("companyId") String companyId);

    @Query("DELETE FROM CompanyExperience ce WHERE ce.id IN :ids")
    void deleteByIds(@Param("ids") List<String> ids);
}
