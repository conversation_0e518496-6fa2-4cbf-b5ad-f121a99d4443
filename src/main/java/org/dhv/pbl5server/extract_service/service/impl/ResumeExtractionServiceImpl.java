package org.dhv.pbl5server.extract_service.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.repository.RedisRepository;
import org.dhv.pbl5server.common_service.utils.LogUtils;
import org.dhv.pbl5server.common_service.utils.MapperUtils;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.authentication_service.repository.AccountRepository;
import org.dhv.pbl5server.common_service.constant.ErrorMessageConstant;
import org.dhv.pbl5server.common_service.constant.RedisCacheConstant;
import org.dhv.pbl5server.extract_service.service.ResumeExtractionService;
import org.dhv.pbl5server.profile_service.entity.Language;
import org.dhv.pbl5server.profile_service.entity.User;
import org.dhv.pbl5server.profile_service.entity.UserAward;
import org.dhv.pbl5server.profile_service.entity.UserEducation;
import org.dhv.pbl5server.profile_service.entity.UserExperience;
import org.dhv.pbl5server.profile_service.mapper.UserMapper;
import org.dhv.pbl5server.profile_service.model.OtherDescription;
import org.dhv.pbl5server.profile_service.repository.LanguageRepository;
import org.dhv.pbl5server.profile_service.repository.UserRepository;
import org.dhv.pbl5server.resume_parse_service.model.ResumeParseResponse;
import org.dhv.pbl5server.s3_service.service.S3Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import okhttp3.*;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ResumeExtractionServiceImpl implements ResumeExtractionService {

    private static final String FOLDER = "resume-extract";
    private static final String RESUME_PARSER_DEBUG_PREFIX = "ResumeParser";

    private final UserMapper userMapper;
    private final S3Service s3Service;
    private final ExtractionService extractionService;
    private final UserRepository userRepository;
    private final LanguageRepository languageRepository;
    private final AccountRepository accountRepository;
    private final RedisRepository redisRepository;
    
    @Value("${application.llama-index.api-url}")
    private String llamaApiUrl;

    @Value("${application.llama-index.api-key}")
    private String llamaApiKey;

    @Value("${application.llama-index.agent-id}")
    private String llamaAgentId;

    private final OkHttpClient httpClient = new OkHttpClient();

    @Override
    public ResumeParseResponse uploadAndExtractResume(Account account, MultipartFile file) {
        try {
            // Step 1: Upload file to S3
            var fileUrlS3 = this.s3Service.uploadFile(FOLDER, file);
            log.info("File uploaded to S3: {}", fileUrlS3);

            // Step 2: Upload file to Llama
            var fileId = this.uploadFileToLlama(file);
            log.info("File uploaded to Llama with ID: {}", fileId);

            // Step 3: Start extraction job
            var jobId = this.startExtractionJob(fileId);
            log.info("Extraction job started with ID: {}", jobId);

            // Step 4: Get extraction result
//            var extractionResult = this.getExtractionResult(jobId);
            var extractionResult = this.extractionService.getExtractionResult(jobId);
            // log.info("Extraction result: {}", extractionResult);

            // Step 5: Update user profile with extracted data
            var updatedUser = this.updateUserProfile(account, extractionResult, fileUrlS3);
            log.info("User profile updated successfully");

            // Step 6: Get updated user profile
//            var updatedProfile = this.userService.getUserProfile(account);

            // Step 6: Update redis
			var userProfileResponse = this.userMapper.toUserProfileResponse(updatedUser);
			this.redisRepository.save(
					RedisCacheConstant.PROFILE_KEY,
					RedisCacheConstant.USER_PROFILE_HASH(account.getAccountId().toString()), 
					userProfileResponse);
            
            return ResumeParseResponse.builder()
                    .userProfile(userProfileResponse)
                    .extractedData(extractionResult)
                    .build();
        } catch (Exception e) {
            log.error("Error during resume extraction", e);
            throw new BadRequestException(ErrorMessageConstant.RESUME_FILE_UPLOAD_FAILED);
        }
    }

    private String uploadFileToLlama(final MultipartFile file) throws IOException {
        var fileExtension = this.getFileExtension(file.getOriginalFilename());
        var mediaType = this.getMediaType(fileExtension);
        if (mediaType == null) {
            throw new BadRequestException("Unsupported file type: " + fileExtension);
        }

        var requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("upload_file", 
                		file.getOriginalFilename(), RequestBody.create(file.getBytes(), mediaType))
                .build();

        var request = new Request.Builder()
                .url(this.llamaApiUrl + "/files")
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + this.llamaApiKey)
                .addHeader("accept", "application/json")
                .build();

        try (var response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to upload file to Llama: {}", response.code());
                throw new BadRequestException("Failed to upload file to Llama");
            }

            var responseBody = response.body().string();
            log.info("Llama File Upload Response: {}", responseBody);

            Map<String, Object> responseMap = MapperUtils.fromJson(responseBody, new TypeReference<>() {
			});
            return (String) responseMap.get("id");
        }
    }

    private String startExtractionJob(final String fileId) throws IOException {
        var payload = Map.of(
                "extraction_agent_id", this.llamaAgentId,
                "file_id", fileId
        );
        var requestBody = RequestBody.create(
                MapperUtils.toJson(payload),
                MediaType.parse("application/json")
        );

        var request = new Request.Builder()
                .url(llamaApiUrl + "/extraction/jobs")
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + this.llamaApiKey)
                .addHeader("accept", "application/json")
                .addHeader("Content-Type", "application/json")
                .build();

        try (var response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to start extraction job: {}", response.code());
                throw new BadRequestException("Failed to start extraction job");
            }

            var responseBody = response.body().string();
            log.info("Llama Extraction Job Response: {}", responseBody);

            Map<String, Object> responseMap = MapperUtils.fromJson(responseBody, new TypeReference<>() {
            });
            return (String) responseMap.get("id");
        }
    }

    private String getJobStatus(final String jobId) throws IOException {
        var request = new Request.Builder()
                .url(this.llamaApiUrl + "/extraction/jobs/" + jobId)
                .get()
                .addHeader("Authorization", "Bearer " + llamaApiKey)
                .addHeader("accept", "application/json")
                .build();

        try (var response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to get job status: {}", response.code());
                throw new BadRequestException("Failed to get job status");
            }

            var responseBody = response.body().string();
            log.info("Llama Job Status Response: {}", responseBody);

            Map<String, Object> responseMap = MapperUtils.fromJson(responseBody, new TypeReference<>() {});
            String status = (String) responseMap.get("status");

            if ("FAILED".equalsIgnoreCase(status)) {
                log.error("Extraction job failed: {}", responseMap.get("error"));
                throw new BadRequestException("Extraction job failed: " + responseMap.get("error"));
            }

            return status;
        }
    }

    @Retryable(
        retryFor = {BadRequestException.class, IOException.class, Exception.class},
        maxAttempts = 10,
        backoff = @Backoff(delay = 5000))
    public Map<String, Object> getExtractionResult(final String jobId) throws IOException {
        // Check job status
        var jobStatus = this.getJobStatus(jobId);
        if (!"COMPLETED".equalsIgnoreCase(jobStatus)) {
            log.error("Extraction job is not yet completed. Current status: {}", jobStatus);
            throw new BadRequestException("Extraction job is not yet completed");
        }

        // Fetch the result
        var request = new Request.Builder()
                .url(this.llamaApiUrl + "/extraction/jobs/" + jobId + "/result")
                .get()
                .addHeader("Authorization", "Bearer " + llamaApiKey)
                .addHeader("accept", "application/json")
                .build();

        try (var response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to get extraction result: {}", response.code());
                throw new BadRequestException("Failed to get extraction result");
            }

            var responseBody = response.body().string();
            log.info("Llama Extraction Result Response: {}", responseBody);

            return MapperUtils.fromJson(responseBody, new TypeReference<>() {});
        }
    }

    /**
     * Extracts the file extension from the file name.
     *
     * @param fileName the name of the file
     * @return the file extension (e.g., "pdf", "jpg"), or an empty string if no extension is found
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * Maps a file extension to a MediaType.
     *
     * @param fileExtension the file extension (e.g., "pdf", "jpg")
     * @return the corresponding MediaType, or null if the file type is unsupported
     */
    private MediaType getMediaType(String fileExtension) {
        switch (fileExtension) {
            case "pdf":
                return MediaType.parse("application/pdf");
            case "jpg":
            case "jpeg":
                return MediaType.parse("image/jpeg");
            case "png":
                return MediaType.parse("image/png");
            default:
                return null; // Unsupported file type
        }
    }
    
    @SuppressWarnings("unchecked")
    private User updateUserProfile(final Account account, final Map<String, Object> extractedData, final String resumeLink) {
        // Get user
        var user = this.userRepository.findById(account.getAccountId())
                .orElseThrow(() -> new BadRequestException(ErrorMessageConstant.USER_NOT_FOUND));

        // Update basic info
        var basicInfo = (Map<String, Object>) extractedData.get("basic_info");
        if (basicInfo != null) {
            this.updateBasicInfo(user, account, basicInfo, resumeLink);
        }

        // Update education
        List<Map<String, Object>> educations = (List<Map<String, Object>>) extractedData.get("education");
        if (educations != null && !educations.isEmpty()) {
            updateEducations(user, educations);
        }

        // Update experiences
        List<Map<String, Object>> experiences = (List<Map<String, Object>>) extractedData.get("experiences");
        if (experiences != null && !experiences.isEmpty()) {
            updateExperiences(user, experiences);
        }

        // Update awards
        List<Map<String, Object>> awards = (List<Map<String, Object>>) extractedData.get("awards");
        if (awards != null && !awards.isEmpty()) {
            updateAwards(user, awards);
        }

        // Update languages
        List<Map<String, Object>> languages = (List<Map<String, Object>>) extractedData.get("languages");
        if (languages != null && !languages.isEmpty()) {
            updateLanguages(account, languages);
        }

        // Update others
        List<Map<String, Object>> others = (List<Map<String, Object>>) extractedData.get("others");
        if (others != null && !others.isEmpty()) {
            updateOthers(user, others);
        }

        // Save user
        user.setAccount(account);
        this.accountRepository.save(account);
        return this.userRepository.save(user);
    }

    @SuppressWarnings("unchecked")
    private void updateBasicInfo(User user, Account account, Map<String, Object> basicInfo, String resumeLink) {
        user.setFirstName((String) basicInfo.get("first_name"));
        user.setLastName((String) basicInfo.get("last_name"));
        user.setGender((Boolean) basicInfo.get("gender"));
        user.setResumeLink(resumeLink);

        // Parse date of birth
        String dobString = (String) basicInfo.get("date_of_birth");
        if (dobString != null && !dobString.isEmpty()) {
            try {
                Instant instant = Instant.parse(dobString);
                user.setDateOfBirth(Timestamp.from(instant));
            } catch (Exception e) {
                LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing date of birth", e);
            }
        }

        user.setSummaryIntroduction((String) basicInfo.get("summary_introduction"));

        // Update social media links
        var socialMediaLinks = (List<String>) basicInfo.get("social_media_links");
        if (!CollectionUtils.isEmpty(socialMediaLinks)) {
        	if (!CollectionUtils.isEmpty(user.getSocialMediaLink())) {
            	user.getSocialMediaLink().clear();
                user.getSocialMediaLink().addAll(socialMediaLinks);
            } else {
            	user.setSocialMediaLink(socialMediaLinks);
            }
        }

        // Update account info
//        account.setEmail((String) basicInfo.get("email"));
        account.setPhoneNumber((String) basicInfo.get("phone_number"));
        account.setAddress((String) basicInfo.get("address"));
    }

    private void updateEducations(User user, List<Map<String, Object>> educations) {
        List<UserEducation> educationEntities = new ArrayList<>();

        for (Map<String, Object> education : educations) {
            UserEducation educationEntity = new UserEducation();
            educationEntity.setUser(user);
            educationEntity.setStudyPlace((String) education.get("study_place"));
            educationEntity.setMajority((String) education.get("majority"));

            // Parse CPA
            Object cpaObj = education.get("cpa");
            if (cpaObj instanceof Number) {
                educationEntity.setCpa(((Number) cpaObj).doubleValue());
            }

            // Parse dates
            String startTimeString = (String) education.get("study_start_time");
            String endTimeString = (String) education.get("study_end_time");

            if (startTimeString != null && !startTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(startTimeString);
                    educationEntity.setStudyStartTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing study start time", e);
                }
            }

            if (endTimeString != null && !endTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(endTimeString);
                    educationEntity.setStudyEndTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing study end time", e);
                }
            }

            educationEntity.setNote((String) education.get("description"));
            educationEntities.add(educationEntity);
        }

        // Replace existing educations
		if (!CollectionUtils.isEmpty(educationEntities)) {
			if (!CollectionUtils.isEmpty(user.getEducations())) {
				user.getEducations().clear();
				user.getEducations().addAll(educationEntities);
			} else {
				user.setEducations(educationEntities);
			}
		}
    }

    private void updateExperiences(User user, List<Map<String, Object>> experiences) {
        List<UserExperience> experienceEntities = new ArrayList<>();

        for (Map<String, Object> experience : experiences) {
            UserExperience experienceEntity = new UserExperience();
            experienceEntity.setUser(user);
            experienceEntity.setWorkPlace((String) experience.get("work_place"));
            experienceEntity.setPosition((String) experience.get("position"));
            experienceEntity.setExperienceTitle((String) experience.get("experience_title"));

            // Parse dates
            String startTimeString = (String) experience.get("experience_start_time");
            String endTimeString = (String) experience.get("experience_end_time");

            if (startTimeString != null && !startTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(startTimeString);
                    experienceEntity.setExperienceStartTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing experience start time", e);
                }
            }

            if (endTimeString != null && !endTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(endTimeString);
                    experienceEntity.setExperienceEndTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing experience end time", e);
                }
            }

            experienceEntity.setNote((String) experience.get("description"));
            experienceEntities.add(experienceEntity);
        }

        // Replace existing experiencesif(!CollectionUtils.isEmpty(experienceEntities)) {
		if (!CollectionUtils.isEmpty(user.getExperiences())) {
			if (!CollectionUtils.isEmpty(user.getExperiences())) {
				user.getExperiences().clear();
				user.getExperiences().addAll(experienceEntities);
			} else {
				user.setExperiences(experienceEntities);
			}
		}
    }

    private void updateAwards(User user, List<Map<String, Object>> awards) {
        List<UserAward> awardEntities = new ArrayList<>();

        for (Map<String, Object> award : awards) {
            UserAward awardEntity = new UserAward();
            awardEntity.setUser(user);
            awardEntity.setCertificateName((String) award.get("certificate_name"));

            // Parse date
            String certificateTimeString = (String) award.get("certificate_time");
            if (certificateTimeString != null && !certificateTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(certificateTimeString);
                    awardEntity.setCertificateTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing certificate time", e);
                }
            }

            awardEntity.setNote((String) award.get("description"));
            awardEntities.add(awardEntity);
        }

        // Replace existing awards
		if (!CollectionUtils.isEmpty(awardEntities)) {
			if (!CollectionUtils.isEmpty(user.getAwards())) {
				user.getAwards().clear();
				user.getAwards().addAll(awardEntities);
			} else {
				user.setAwards(awardEntities);
			}
		}
    }

    private void updateLanguages(Account account, List<Map<String, Object>> languages) {
        List<Language> languageEntities = new ArrayList<>();

        for (Map<String, Object> language : languages) {
            Language languageEntity = new Language();
            languageEntity.setAccount(account);
            languageEntity.setLanguageName((String) language.get("language_name"));
            languageEntity.setLanguageScore((String) language.get("language_score"));
            languageEntity.setLanguageCertificateName((String) language.get("language_certificate_name"));

            // Parse date
            String certificateDateString = (String) language.get("language_certificate_date");
            if (certificateDateString != null && !certificateDateString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(certificateDateString);
                    languageEntity.setLanguageCertificateDate(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing language certificate date", e);
                }
            }

            languageEntities.add(languageEntity);
        }

        // Replace existing languages
        languageRepository.deleteAllByAccountId(account.getAccountId());
        languageRepository.saveAll(languageEntities);
    }

    private void updateOthers(User user, List<Map<String, Object>> others) {
        List<OtherDescription> otherDescriptions = new ArrayList<>();

        for (Map<String, Object> other : others) {
            OtherDescription otherDescription = new OtherDescription();
            otherDescription.setTitle((String) other.get("title"));
            otherDescription.setDescription((String) other.get("description"));
            otherDescriptions.add(otherDescription);
        }

        // Replace existing others
		if (!CollectionUtils.isEmpty(otherDescriptions)) {
			if (!CollectionUtils.isEmpty(user.getOthers())) {
				user.getOthers().clear();
				user.getOthers().addAll(otherDescriptions);
			} else {
				user.setOthers(otherDescriptions);
			}
		}
    }
}
