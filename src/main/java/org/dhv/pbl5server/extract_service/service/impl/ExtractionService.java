package org.dhv.pbl5server.extract_service.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.utils.MapperUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class ExtractionService {

    private final OkHttpClient httpClient;
    private final String llamaApiUrl;
    private final String llamaApiKey;

    public ExtractionService(
    		@Value("${application.llama-index.api-url}") String llamaApiUrl,
    		@Value("${application.llama-index.api-key}") String llamaApiKey) {
        this.httpClient = new OkHttpClient();
        this.llamaApiUrl = llamaApiUrl;
        this.llamaApiKey = llamaApiKey;
    }

    @SuppressWarnings("unchecked")
	@Retryable(
        retryFor = {BadRequestException.class, IOException.class, Exception.class},
        maxAttempts = 20,
        backoff = @Backoff(delay = 5000))
    public Map<String, Object> getExtractionResult(final String jobId) throws IOException {
        var jobStatus = getJobStatus(jobId);

        if (!"SUCCESS".equalsIgnoreCase(jobStatus)) {
            log.error("Extraction job is not yet completed. Current status: {}", jobStatus);
            throw new BadRequestException("Extraction job is not yet completed");
        }

        var request = new Request.Builder()
                .url(this.llamaApiUrl + "/extraction/jobs/" + jobId + "/result")
                .get()
                .addHeader("Authorization", "Bearer " + llamaApiKey)
                .addHeader("accept", "application/json")
                .build();

        try (Response response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to get extraction result: {}", response.code());
                throw new BadRequestException("Failed to get extraction result");
            }

            var responseBody = response.body().string();
            log.info("Llama Extraction Result Response: {}", responseBody);
            Map<String, Object> responseJson = MapperUtils.fromJson(responseBody, new TypeReference<>() {});
            return (Map<String, Object>) responseJson.get("data");
        }
    }

    @Recover
    public Map<String, Object> recover(Exception ex, String jobId) {
        log.error("Retries exhausted for jobId: {}. Error: {}", jobId, ex.getMessage());
        return Map.of("error", "Failed to get extraction result after retries");
    }

    private String getJobStatus(final String jobId) throws IOException {
        var request = new Request.Builder()
                .url(this.llamaApiUrl + "/extraction/jobs/" + jobId)
                .get()
                .addHeader("Authorization", "Bearer " + this.llamaApiKey)
                .addHeader("accept", "application/json")
                .build();

        try (var response = this.httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to get job status: {}", response.code());
                throw new BadRequestException("Failed to get job status");
            }

            var responseBody = response.body().string();
            log.info("Llama Job Status Response: {}", responseBody);

            Map<String, Object> responseMap = MapperUtils.fromJson(responseBody, new TypeReference<>() {});
            String status = (String) responseMap.get("status");

            if ("FAILED".equalsIgnoreCase(status)) {
                log.error("Extraction job failed: {}", responseMap.get("error"));
                throw new BadRequestException("Extraction job failed: " + responseMap.get("error"));
            }

            return status;
        }
    }
}
