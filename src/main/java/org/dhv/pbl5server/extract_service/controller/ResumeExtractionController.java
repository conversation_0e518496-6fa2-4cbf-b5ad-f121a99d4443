package org.dhv.pbl5server.extract_service.controller;

import lombok.RequiredArgsConstructor;

import org.dhv.pbl5server.authentication_service.annotation.CurrentAccount;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.dhv.pbl5server.extract_service.service.ResumeExtractionService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(path = "/v1/resume-extract")
@RequiredArgsConstructor
public class ResumeExtractionController {

	private final ResumeExtractionService resumeExtractionService;

	@PostMapping(path = "/upload")
	public ResponseEntity<ApiDataResponse> uploadAndExtractResume(
			@RequestParam("file") MultipartFile file, 
			@CurrentAccount Account account) {
		var extractedData = this.resumeExtractionService.uploadAndExtractResume(account, file);
		return ResponseEntity.ok(ApiDataResponse.successWithoutMeta(extractedData));
	}
}
