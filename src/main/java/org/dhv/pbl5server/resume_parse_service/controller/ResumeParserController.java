package org.dhv.pbl5server.resume_parse_service.controller;

import lombok.RequiredArgsConstructor;
import org.dhv.pbl5server.authentication_service.annotation.CurrentAccount;
import org.dhv.pbl5server.authentication_service.annotation.PreAuthorizeUser;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.dhv.pbl5server.resume_parse_service.service.ResumeParserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/v1/resume-parser")
@RequiredArgsConstructor
public class ResumeParserController {
    private final ResumeParserService resumeParserService;
    
    @PreAuthorizeUser
    @PostMapping("/parse")
    public ResponseEntity<ApiDataResponse> parseResume(
            @RequestPart("file") MultipartFile file,
            @CurrentAccount Account account
    ) {
        return ResponseEntity.ok(ApiDataResponse.successWithoutMeta(
                resumeParserService.parseResumeAndUpdateProfile(account, file)
        ));
    }
}
