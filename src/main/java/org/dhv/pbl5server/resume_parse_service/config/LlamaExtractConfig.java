package org.dhv.pbl5server.resume_parse_service.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class LlamaExtractConfig {
    @Value("${application.llama-index.api-url}")
    private String apiUrl;

    @Value("${application.llama-index.api-key}")
    private String apiKey;

    @Value("${application.llama-index.project-id:}")
    private String projectId;

    // Extraction agent endpoints
    public String getCreateExtractionAgentUrl() {
        return apiUrl + "/extraction/extraction-agents" +
                (projectId != null && !projectId.isEmpty() ? "?project_id=" + projectId : "");
    }

    public String getExtractionAgentByNameUrl(String agentName) {
        return apiUrl + "/extraction/extraction-agents/by-name/" + agentName +
                (projectId != null && !projectId.isEmpty() ? "?project_id=" + projectId : "");
    }

    // File upload endpoint
    public String getFileUploadUrl() {
        return apiUrl + "/files";
    }

    // Extraction job endpoints
    public String getCreateExtractionJobUrl() {
        return apiUrl + "/extraction/jobs";
    }

    public String getExtractionJobStatusUrl(String jobId) {
        return apiUrl + "/extraction/jobs/" + jobId;
    }

    public String getExtractionJobResultUrl(String jobId) {
        return apiUrl + "/extraction/jobs/" + jobId + "/result";
    }
}
