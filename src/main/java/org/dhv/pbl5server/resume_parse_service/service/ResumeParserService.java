package org.dhv.pbl5server.resume_parse_service.service;

import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.profile_service.payload.response.UserProfileResponse;
import org.dhv.pbl5server.resume_parse_service.model.ResumeParseResponse;
import org.springframework.web.multipart.MultipartFile;

public interface ResumeParserService {
    /**
     * Parse a resume file and update the user profile
     * @param account User account
     * @param file Resume file
     * @return Updated user profile
     */
    ResumeParseResponse parseResumeAndUpdateProfile(Account account, MultipartFile file);
}
