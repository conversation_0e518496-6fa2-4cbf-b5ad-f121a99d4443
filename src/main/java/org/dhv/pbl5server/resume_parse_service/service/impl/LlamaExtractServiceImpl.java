package org.dhv.pbl5server.resume_parse_service.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.dhv.pbl5server.common_service.constant.ErrorMessageConstant;
import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.utils.LogUtils;
import org.dhv.pbl5server.resume_parse_service.config.LlamaExtractConfig;
import org.dhv.pbl5server.resume_parse_service.model.*;
import org.dhv.pbl5server.resume_parse_service.service.LlamaExtractService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Service
@RequiredArgsConstructor
@Slf4j
public class LlamaExtractServiceImpl implements LlamaExtractService {
    private final LlamaExtractConfig config;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private static final String LLAMA_DEBUG_PREFIX = "LlamaExtract";
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    @Override
    public ExtractionAgent createExtractionAgent(String name, String schemaJson) {
        try {
            RequestBody body = RequestBody.create(schemaJson, JSON);
            Request request = new Request.Builder()
                    .url(config.getCreateExtractionAgentUrl())
                    .post(body)
                    .addHeader("accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX, "Failed to create extraction agent: " + response.code());
                    throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, ExtractionAgent.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error creating extraction agent", e);
            throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
        }
    }

    @Override
    public ExtractionAgent getExtractionAgentByName(String name) {
        try {
            // Log API key (chỉ để debug, xóa trong production)
            LogUtils.info(LLAMA_DEBUG_PREFIX, "API URL: " + config.getApiUrl());
            LogUtils.info(LLAMA_DEBUG_PREFIX,
                    "API Key length: " + (config.getApiKey() != null ? config.getApiKey().length() : 0));

            Request request = new Request.Builder()
                    .url(config.getExtractionAgentByNameUrl(name))
                    .get()
                    .addHeader("accept", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX,
                            "Failed to get extraction agent: " + response.code() + " - " + response.message());
                    if (response.body() != null) {
                        String errorBody = response.body().string();
                        LogUtils.error(LLAMA_DEBUG_PREFIX, "Error response: " + errorBody);
                    }
                    throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, ExtractionAgent.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error getting extraction agent", e);
            throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
        }
    }

    @Override
    public FileUploadResponse uploadFile(MultipartFile file) {
        try {
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("upload_file", file.getOriginalFilename(),
                            RequestBody.create(file.getBytes(), MediaType.parse(file.getContentType())))
                    .build();

            Request request = new Request.Builder()
                    .url(config.getFileUploadUrl())
                    .post(requestBody)
                    .addHeader("accept", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX, "Failed to upload file: " + response.code());
                    throw new BadRequestException(ErrorMessageConstant.RESUME_FILE_UPLOAD_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, FileUploadResponse.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error uploading file", e);
            throw new BadRequestException(ErrorMessageConstant.RESUME_FILE_UPLOAD_FAILED);
        }
    }

    @Override
    public ExtractionJobResponse createExtractionJob(String extractionAgentId, String fileId) {
        try {
            ExtractionJobRequest jobRequest = ExtractionJobRequest.builder()
                    .extractionAgentId(extractionAgentId)
                    .fileId(fileId)
                    .build();

            String requestJson = objectMapper.writeValueAsString(jobRequest);
            RequestBody body = RequestBody.create(requestJson, JSON);

            HttpUrl.Builder urlBuilder = HttpUrl.parse(config.getCreateExtractionJobUrl()).newBuilder();

            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .post(body)
                    .addHeader("accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX, "Failed to create extraction job: " + response.code());
                    throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_CREATION_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, ExtractionJobResponse.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error creating extraction job", e);
            throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_CREATION_FAILED);
        }
    }

    @Override
    public ExtractionJobResponse getExtractionJobStatus(String jobId) {
        try {
            Request request = new Request.Builder()
                    .url(config.getExtractionJobStatusUrl(jobId))
                    .get()
                    .addHeader("accept", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX, "Failed to get extraction job status: " + response.code());
                    throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, ExtractionJobResponse.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error getting extraction job status", e);
            throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_FAILED);
        }
    }

    @Override
    public ExtractionResultResponse getExtractionJobResult(String jobId) {
        try {
            Request request = new Request.Builder()
                    .url(config.getExtractionJobResultUrl(jobId))
                    .get()
                    .addHeader("accept", "application/json")
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    LogUtils.error(LLAMA_DEBUG_PREFIX, "Failed to get extraction job result: " + response.code());
                    throw new BadRequestException(ErrorMessageConstant.EXTRACTION_RESULT_FAILED);
                }

                String responseBody = response.body().string();
                return objectMapper.readValue(responseBody, ExtractionResultResponse.class);
            }
        } catch (IOException e) {
            LogUtils.error(LLAMA_DEBUG_PREFIX, "Error getting extraction job result", e);
            throw new BadRequestException(ErrorMessageConstant.EXTRACTION_RESULT_FAILED);
        }
    }
}
