package org.dhv.pbl5server.resume_parse_service.service;

import org.dhv.pbl5server.resume_parse_service.model.*;
import org.springframework.web.multipart.MultipartFile;

public interface LlamaExtractService {
    /**
     * Create a new extraction agent
     * @param name Agent name
     * @param schemaJson Schema JSON as string
     * @return Created extraction agent
     */
    ExtractionAgent createExtractionAgent(String name, String schemaJson);
    
    /**
     * Get an extraction agent by name
     * @param name Agent name
     * @return Extraction agent
     */
    ExtractionAgent getExtractionAgentByName(String name);
    
    /**
     * Upload a file to LlamaExtract
     * @param file File to upload
     * @return File upload response
     */
    FileUploadResponse uploadFile(MultipartFile file);
    
    /**
     * Create an extraction job
     * @param extractionAgentId Extraction agent ID
     * @param fileId File ID
     * @return Extraction job response
     */
    ExtractionJobResponse createExtractionJob(String extractionAgentId, String fileId);
    
    /**
     * Get extraction job status
     * @param jobId Job ID
     * @return Extraction job response
     */
    ExtractionJobResponse getExtractionJobStatus(String jobId);
    
    /**
     * Get extraction job result
     * @param jobId Job ID
     * @return Extraction result response
     */
    ExtractionResultResponse getExtractionJobResult(String jobId);
}
