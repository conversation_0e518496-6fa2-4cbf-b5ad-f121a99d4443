package org.dhv.pbl5server.resume_parse_service.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.constant.ErrorMessageConstant;
import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.utils.LogUtils;
import org.dhv.pbl5server.profile_service.entity.Language;
import org.dhv.pbl5server.profile_service.entity.User;
import org.dhv.pbl5server.profile_service.entity.UserAward;
import org.dhv.pbl5server.profile_service.entity.UserEducation;
import org.dhv.pbl5server.profile_service.entity.UserExperience;
import org.dhv.pbl5server.profile_service.model.OtherDescription;
import org.dhv.pbl5server.profile_service.payload.response.UserProfileResponse;
import org.dhv.pbl5server.profile_service.repository.LanguageRepository;
import org.dhv.pbl5server.profile_service.repository.UserRepository;
import org.dhv.pbl5server.profile_service.service.UserService;
import org.dhv.pbl5server.resume_parse_service.model.ExtractionJobResponse;
import org.dhv.pbl5server.resume_parse_service.model.ExtractionResultResponse;
import org.dhv.pbl5server.resume_parse_service.model.FileUploadResponse;
import org.dhv.pbl5server.resume_parse_service.model.ResumeParseResponse;
import org.dhv.pbl5server.resume_parse_service.service.LlamaExtractService;
import org.dhv.pbl5server.resume_parse_service.service.ResumeParserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResumeParserServiceImpl implements ResumeParserService {
    private final LlamaExtractService llamaExtractService;
    private final UserService userService;
    private final UserRepository userRepository;
    private final LanguageRepository languageRepository;

    private static final String RESUME_PARSER_DEBUG_PREFIX = "ResumeParser";
    private static final String EXTRACTION_AGENT_NAME = "resume_parser";
    private static final String JOB_STATUS_SUCCESS = "SUCCESS";
    private static final int MAX_POLL_ATTEMPTS = 30;
    private static final int POLL_INTERVAL_MS = 2000;

    @Override
    @Transactional
    public ResumeParseResponse parseResumeAndUpdateProfile(Account account, MultipartFile file) {
        try {
            // 1. Get or create extraction agent
            String agentId = getExtractionAgentId();

            // 2. Upload file
            FileUploadResponse fileUploadResponse = llamaExtractService.uploadFile(file);

            // 3. Create extraction job
            ExtractionJobResponse jobResponse = llamaExtractService.createExtractionJob(agentId,
                    fileUploadResponse.getFileId());

            // 4. Poll for job completion
            ExtractionJobResponse completedJob = pollForJobCompletion(jobResponse.getId());

            // 5. Get extraction results
            ExtractionResultResponse resultResponse = llamaExtractService.getExtractionJobResult(completedJob.getId());

            // 6. Process results and update user profile
            Map<String, Object> extractedData = resultResponse.getExtractions().get(0);
            updateUserProfile(account, extractedData);

            // 7. Get updated user profile
            UserProfileResponse updatedProfile = userService.getUserProfile(account);

            return ResumeParseResponse.builder()
                    .userProfile(updatedProfile)
                    .extractedData(extractedData)
                    .build();

        } catch (Exception e) {
            LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing resume", e);
            throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
        }
    }

    private String getExtractionAgentId() {
        try {
            // Try to get existing agent
            var agent = llamaExtractService.getExtractionAgentByName(EXTRACTION_AGENT_NAME);
            return agent.getId();
        } catch (Exception e) {
            // Create new agent if not found
            try {
                String schemaJson = loadSchemaJson();
                var agent = llamaExtractService.createExtractionAgent(EXTRACTION_AGENT_NAME, schemaJson);
                return agent.getId();
            } catch (Exception ex) {
                LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error creating extraction agent", ex);
                throw new BadRequestException(ErrorMessageConstant.RESUME_PARSE_FAILED);
            }
        }
    }

    private String loadSchemaJson() throws IOException {
        try {
            // Thử đọc từ classpath (hoạt động trong cả development và production)
            org.springframework.core.io.Resource resource = new org.springframework.core.io.ClassPathResource(
                    "org/dhv/pbl5server/resume_parse_service/schema.json");
            return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error loading schema from classpath", e);

            // Fallback: thử đọc trực tiếp từ file system
            String schemaPath = "src/main/java/org/dhv/pbl5server/resume_parse_service/schema.json";
            return new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(schemaPath)),
                    StandardCharsets.UTF_8);
        }
    }

    private ExtractionJobResponse pollForJobCompletion(String jobId) {
        int attempts = 0;
        ExtractionJobResponse jobResponse = null;

        while (attempts < MAX_POLL_ATTEMPTS) {
            try {
                Thread.sleep(POLL_INTERVAL_MS);
                jobResponse = llamaExtractService.getExtractionJobStatus(jobId);

                if (JOB_STATUS_SUCCESS.equals(jobResponse.getStatus())) {
                    return jobResponse;
                }

                attempts++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_FAILED);
            }
        }

        throw new BadRequestException(ErrorMessageConstant.EXTRACTION_JOB_FAILED);
    }

    @SuppressWarnings("unchecked")
    private void updateUserProfile(Account account, Map<String, Object> extractedData) {
        // Get user
        User user = userRepository.findById(account.getAccountId())
                .orElseThrow(() -> new BadRequestException(ErrorMessageConstant.USER_NOT_FOUND));

        // Update basic info
        Map<String, Object> basicInfo = (Map<String, Object>) extractedData.get("basic_info");
        if (basicInfo != null) {
            updateBasicInfo(user, account, basicInfo);
        }

        // Update education
        List<Map<String, Object>> educations = (List<Map<String, Object>>) extractedData.get("education");
        if (educations != null && !educations.isEmpty()) {
            updateEducations(user, educations);
        }

        // Update experiences
        List<Map<String, Object>> experiences = (List<Map<String, Object>>) extractedData.get("experiences");
        if (experiences != null && !experiences.isEmpty()) {
            updateExperiences(user, experiences);
        }

        // Update awards
        List<Map<String, Object>> awards = (List<Map<String, Object>>) extractedData.get("awards");
        if (awards != null && !awards.isEmpty()) {
            updateAwards(user, awards);
        }

        // Update languages
        List<Map<String, Object>> languages = (List<Map<String, Object>>) extractedData.get("languages");
        if (languages != null && !languages.isEmpty()) {
            updateLanguages(account, languages);
        }

        // Update others
        List<Map<String, Object>> others = (List<Map<String, Object>>) extractedData.get("others");
        if (others != null && !others.isEmpty()) {
            updateOthers(user, others);
        }

        // Save user
        userRepository.save(user);
    }

    @SuppressWarnings("unchecked")
    private void updateBasicInfo(User user, Account account, Map<String, Object> basicInfo) {
        user.setFirstName((String) basicInfo.get("first_name"));
        user.setLastName((String) basicInfo.get("last_name"));
        user.setGender((Boolean) basicInfo.get("gender"));

        // Parse date of birth
        String dobString = (String) basicInfo.get("date_of_birth");
        if (dobString != null && !dobString.isEmpty()) {
            try {
                Instant instant = Instant.parse(dobString);
                user.setDateOfBirth(Timestamp.from(instant));
            } catch (Exception e) {
                LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing date of birth", e);
            }
        }

        user.setSummaryIntroduction((String) basicInfo.get("summary_introduction"));

        // Update social media links
        List<String> socialMediaLinks = (List<String>) basicInfo.get("social_media_links");
        if (socialMediaLinks != null) {
            user.setSocialMediaLink(socialMediaLinks);
        }

        // Update account info
        account.setEmail((String) basicInfo.get("email"));
        account.setPhoneNumber((String) basicInfo.get("phone_number"));
        account.setAddress((String) basicInfo.get("address"));
    }

    private void updateEducations(User user, List<Map<String, Object>> educations) {
        List<UserEducation> educationEntities = new ArrayList<>();

        for (Map<String, Object> education : educations) {
            UserEducation educationEntity = new UserEducation();
            educationEntity.setUser(user);
            educationEntity.setStudyPlace((String) education.get("study_place"));
            educationEntity.setMajority((String) education.get("majority"));

            // Parse CPA
            Object cpaObj = education.get("cpa");
            if (cpaObj instanceof Number) {
                educationEntity.setCpa(((Number) cpaObj).doubleValue());
            }

            // Parse dates
            String startTimeString = (String) education.get("study_start_time");
            String endTimeString = (String) education.get("study_end_time");

            if (startTimeString != null && !startTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(startTimeString);
                    educationEntity.setStudyStartTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing study start time", e);
                }
            }

            if (endTimeString != null && !endTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(endTimeString);
                    educationEntity.setStudyEndTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing study end time", e);
                }
            }

            educationEntity.setNote((String) education.get("description"));
            educationEntities.add(educationEntity);
        }

        // Replace existing educations
        user.setEducations(educationEntities);
    }

    private void updateExperiences(User user, List<Map<String, Object>> experiences) {
        List<UserExperience> experienceEntities = new ArrayList<>();

        for (Map<String, Object> experience : experiences) {
            UserExperience experienceEntity = new UserExperience();
            experienceEntity.setUser(user);
            experienceEntity.setWorkPlace((String) experience.get("work_place"));
            experienceEntity.setPosition((String) experience.get("position"));
            experienceEntity.setExperienceTitle((String) experience.get("experience_title"));

            // Parse dates
            String startTimeString = (String) experience.get("experience_start_time");
            String endTimeString = (String) experience.get("experience_end_time");

            if (startTimeString != null && !startTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(startTimeString);
                    experienceEntity.setExperienceStartTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing experience start time", e);
                }
            }

            if (endTimeString != null && !endTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(endTimeString);
                    experienceEntity.setExperienceEndTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing experience end time", e);
                }
            }

            experienceEntity.setNote((String) experience.get("description"));
            experienceEntities.add(experienceEntity);
        }

        // Replace existing experiences
        user.setExperiences(experienceEntities);
    }

    private void updateAwards(User user, List<Map<String, Object>> awards) {
        List<UserAward> awardEntities = new ArrayList<>();

        for (Map<String, Object> award : awards) {
            UserAward awardEntity = new UserAward();
            awardEntity.setUser(user);
            awardEntity.setCertificateName((String) award.get("certificate_name"));

            // Parse date
            String certificateTimeString = (String) award.get("certificate_time");
            if (certificateTimeString != null && !certificateTimeString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(certificateTimeString);
                    awardEntity.setCertificateTime(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing certificate time", e);
                }
            }

            awardEntity.setNote((String) award.get("description"));
            awardEntities.add(awardEntity);
        }

        // Replace existing awards
        user.setAwards(awardEntities);
    }

    private void updateLanguages(Account account, List<Map<String, Object>> languages) {
        List<Language> languageEntities = new ArrayList<>();

        for (Map<String, Object> language : languages) {
            Language languageEntity = new Language();
            languageEntity.setAccount(account);
            languageEntity.setLanguageName((String) language.get("language_name"));
            languageEntity.setLanguageScore((String) language.get("language_score"));
            languageEntity.setLanguageCertificateName((String) language.get("language_certificate_name"));

            // Parse date
            String certificateDateString = (String) language.get("language_certificate_date");
            if (certificateDateString != null && !certificateDateString.isEmpty()) {
                try {
                    Instant instant = Instant.parse(certificateDateString);
                    languageEntity.setLanguageCertificateDate(Timestamp.from(instant));
                } catch (Exception e) {
                    LogUtils.error(RESUME_PARSER_DEBUG_PREFIX, "Error parsing language certificate date", e);
                }
            }

            languageEntities.add(languageEntity);
        }

        // Replace existing languages
        languageRepository.deleteAllByAccountId(account.getAccountId());
        languageRepository.saveAll(languageEntities);
    }

    private void updateOthers(User user, List<Map<String, Object>> others) {
        List<OtherDescription> otherDescriptions = new ArrayList<>();

        for (Map<String, Object> other : others) {
            OtherDescription otherDescription = new OtherDescription();
            otherDescription.setTitle((String) other.get("title"));
            otherDescription.setDescription((String) other.get("description"));
            otherDescriptions.add(otherDescription);
        }

        // Replace existing others
        user.setOthers(otherDescriptions);
    }
}
