package org.dhv.pbl5server.resume_parse_service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtractionResultResponse {
    private String id;
    
    @JsonProperty("extraction_agent_id")
    private String extractionAgentId;
    
    @JsonProperty("file_id")
    private String fileId;
    
    private List<Map<String, Object>> extractions;
}
