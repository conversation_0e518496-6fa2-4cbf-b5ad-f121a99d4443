package org.dhv.pbl5server.resume_parse_service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractionConfig {
    @JsonProperty("extraction_target")
    private String extractionTarget;
    
    @JsonProperty("extraction_mode")
    private String extractionMode;
    
    @JsonProperty("system_prompt")
    private String systemPrompt;
    
    @JsonProperty("use_reasoning")
    private Boolean useReasoning;
    
    @JsonProperty("cite_sources")
    private Boolean citeSources;
}
