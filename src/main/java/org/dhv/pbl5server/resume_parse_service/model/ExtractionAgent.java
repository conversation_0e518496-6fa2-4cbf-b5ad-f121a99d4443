package org.dhv.pbl5server.resume_parse_service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractionAgent {
    private String id;
    private String name;
    
    @JsonProperty("data_schema")
    private Object dataSchema;
    
    private ExtractionConfig config;
}
