package org.dhv.pbl5server.resume_parse_service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtractionJobResponse {
    private String id;
    
    private String status;
    
    @JsonProperty("extraction_agent_id")
    private String extractionAgentId;
    
    @JsonProperty("file_id")
    private String fileId;
    
    @JsonProperty("created_at")
    private String createdAt;
    
    @JsonProperty("updated_at")
    private String updatedAt;
}
