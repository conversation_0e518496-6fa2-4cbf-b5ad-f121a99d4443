package org.dhv.pbl5server.s3_service.service.impl;

import lombok.RequiredArgsConstructor;
import org.dhv.pbl5server.common_service.constant.ErrorMessageConstant;
import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.utils.CommonUtils;
import org.dhv.pbl5server.common_service.utils.LogUtils;
import org.dhv.pbl5server.s3_service.config.S3ApplicationProperty;
import org.dhv.pbl5server.s3_service.service.S3Service;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URL;
import java.util.List;

@Service
@RequiredArgsConstructor
public class S3ServiceImpl implements S3Service {
    private final S3ApplicationProperty s3Config;
    private final S3Client s3;
    private final String BUCKET_URL = "https://job-swipe-server-s3.s3.ap-southeast-2.amazonaws.com/";
    private final String S3_DEBUG_PREFIX = "S3";

    public String uploadFile(MultipartFile file) {
        try {
            var fileName = this.generateFileName(file);
            this.s3.putObject(this.s3Config.putObjectRequest(fileName), RequestBody.fromBytes(file.getBytes()));
            return "%s%s".formatted(BUCKET_URL, fileName);
        } catch (Exception e) {
            LogUtils.error(S3_DEBUG_PREFIX, "Error occurred while uploading file:", e);
            throw new BadRequestException(ErrorMessageConstant.UPLOAD_FILE_FAILED);
        }
    }

    public String uploadFile(MultipartFile replacedFile, String oldUrl) {
        try {
            var oldFileName = this.getFileName(oldUrl);
            if (CommonUtils.isEmptyOrNullString(oldFileName) || !deleteFile(oldFileName))
                throw new BadRequestException(ErrorMessageConstant.DELETE_FILE_FAILED);
            var fileName = this.generateFileName(replacedFile);
            this.s3.putObject(this.s3Config.putObjectRequest(fileName), RequestBody.fromBytes(replacedFile.getBytes()));
            return "%s%s".formatted(BUCKET_URL, fileName);
        } catch (Exception e) {
            LogUtils.error(S3_DEBUG_PREFIX, "Error occurred while uploading file:", e);
            throw new BadRequestException(ErrorMessageConstant.UPLOAD_FILE_FAILED);
        }
    }

    public List<String> uploadFiles(List<MultipartFile> files) {
        return files.stream().map(file -> {
            try {
                return uploadFile(file);
            } catch (Exception e) {
                LogUtils.error(S3_DEBUG_PREFIX, "Error occurred while uploading file:", e);
                throw new BadRequestException(ErrorMessageConstant.UPLOAD_FILE_FAILED);
            }
        }).toList();
    }

    public List<String> uploadFiles(List<MultipartFile> replacedFiles, List<String> oldUrls) {
        var oldFileNames = oldUrls.stream().map(this::getFileName).toList();
        s3.deleteObjects(s3Config.deleteObjectsRequest(oldFileNames));
        return replacedFiles.stream().map(file -> {
            try {
                return uploadFile(file);
            } catch (Exception e) {
                LogUtils.error(S3_DEBUG_PREFIX, "Error occurred while uploading file:", e);
                throw new BadRequestException(ErrorMessageConstant.UPLOAD_FILE_FAILED);
            }
        }).toList();
    }

    @Override
    public String getFileName(String fileUrl) {
        if (CommonUtils.isEmptyOrNullString(fileUrl))
            return null;
        var arr = fileUrl.split("/");
        return arr[arr.length - 1];
    }

    public String getFileUrl(String fileName) {
        try {
            URL url = s3.utilities().getUrl(s3Config.getUrlRequest(fileName));
            return url.toString();
        } catch (Exception e) {
            LogUtils.error(S3_DEBUG_PREFIX, "Getting file url:", e);
            throw new BadRequestException(ErrorMessageConstant.FILE_NOT_FOUND);
        }
    }

    private boolean deleteFile(String fileName) {
        s3.deleteObject(s3Config.deleteObjectRequest(fileName));
        LogUtils.info(S3_DEBUG_PREFIX, "Deleted file:", fileName);
        return true;
    }

    private String generateFileName(MultipartFile file) {
        var originalName = file.getOriginalFilename();
        if (originalName == null) {
            originalName = "file";
        } else {
            // Normalize separators and get only the base name
            originalName = originalName.replaceAll("\\\\", "/");
            int lastSlash = originalName.lastIndexOf('/');
            if (lastSlash >= 0) {
                originalName = originalName.substring(lastSlash + 1);
            }
        }
        // Remove unsafe characters (keep letters, numbers, dot, dash, underscore)
        originalName = originalName.replaceAll("[^a-zA-Z0-9._-]", "_");

        // Optionally, limit length to 100 chars
        if (originalName.length() > 100) {
            int dotIndex = originalName.lastIndexOf('.');
            String extension = (dotIndex >= 0) ? originalName.substring(dotIndex) : "";
            String baseName = (dotIndex >= 0) ? originalName.substring(0, dotIndex) : originalName;
            int maxBaseLength = 100 - extension.length();
            if (baseName.length() > maxBaseLength) {
                baseName = baseName.substring(0, maxBaseLength);
            }
            originalName = baseName + extension;
        }

        var fileName = "%s-%s"
                .formatted(CommonUtils.getCurrentTimestamp().toString(), originalName)
                .replaceAll(" ", "_");
        LogUtils.info(S3_DEBUG_PREFIX, "Generated file name:", fileName);
        return fileName;
    }

    @Override
    public String uploadFile(String folder, MultipartFile file) {
        try {
            var fileName = folder + "/" + this.generateFileName(file);
            this.s3.putObject(this.s3Config.putObjectRequest(fileName), RequestBody.fromBytes(file.getBytes()));
            return "%s%s".formatted(BUCKET_URL, fileName);
        } catch (Exception e) {
            LogUtils.error(S3_DEBUG_PREFIX, "Error occurred while uploading file:", e);
            throw new BadRequestException(ErrorMessageConstant.UPLOAD_FILE_FAILED);
        }
    }
}
