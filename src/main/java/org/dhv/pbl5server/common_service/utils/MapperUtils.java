package org.dhv.pbl5server.common_service.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

public class MapperUtils {
    // Singleton instance of ObjectMapper for JSON processing
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .findAndRegisterModules()
            .configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .enable(com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT)
            .setPropertyNamingStrategy(com.fasterxml.jackson.databind.PropertyNamingStrategies.SNAKE_CASE);

    /**
     * Converts a JSON string to an object of the specified class type.
     *
     * @param json the JSON string to convert
     * @param clazz the class type to convert the JSON string into
     * @param <T> the type of the class
     * @return an object of type T
     * @throws IllegalArgumentException if the JSON string cannot be converted
     */
    public static <T> T fromJson(final String json, final Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Failed to parse JSON string to " + clazz.getSimpleName(), e);
        }
    }

    /**
     * Converts a JSON string to an object of the specified type using TypeReference.
     *
     * @param json the JSON string to convert
     * @param typeReference the TypeReference to specify the target type
     * @param <T> the type of the class
     * @return an object of type T
     * @throws IllegalArgumentException if the JSON string cannot be converted
     */
    public static <T> T fromJson(final String json, final TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Failed to parse JSON string to the specified type", e);
        }
    }

    /**
     * Converts an object to a JSON string.
     *
     * @param object the object to convert
     * @return the JSON string representation of the object
     * @throws IllegalArgumentException if the object cannot be converted to JSON
     */
    public static String toJson(final Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Failed to convert object to JSON string", e);
        }
    }

    /**
     * Returns the singleton ObjectMapper instance.
     *
     * @return the ObjectMapper instance
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
}
