package org.dhv.pbl5server.firebase_service.payload;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonSnakeCaseNaming
public class FCMTokenRequest {
    
    @NotBlank(message = "FCM token không được để trống")
    private String fcmToken;
} 