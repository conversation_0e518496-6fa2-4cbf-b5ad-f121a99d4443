package org.dhv.pbl5server.firebase_service.payload;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MessageRequest {

	@NotBlank
	private String conversationId;

	private String messageContent; // Optional for file uploads

	@NotBlank
	private String fcmToken;
}
