package org.dhv.pbl5server.firebase_service.payload;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSnakeCaseNaming
public class MatchNotificationRequest {
    
    @NotBlank(message = "ID người nhận không được để trống")
    private String recipientId;
    
    @NotBlank(message = "Tên đối tượng match không được để trống")
    private String matchedWithName;
    
    @NotBlank(message = "ID match không được để trống")
    private String matchId;
    
    @NotBlank(message = "FCM token không được để trống")
    private String fcmToken;
} 