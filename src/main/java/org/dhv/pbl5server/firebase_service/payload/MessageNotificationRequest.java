package org.dhv.pbl5server.firebase_service.payload;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dhv.pbl5server.common_service.annotation.JsonSnakeCaseNaming;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSnakeCaseNaming
public class MessageNotificationRequest {
    
    @NotBlank(message = "ID người nhận không được để trống")
    private String recipientId;
    
    @NotBlank(message = "Tên người gửi không được để trống")
    private String senderName;
    
    @NotBlank(message = "Nội dung tin nhắn không được để trống")
    private String messageContent;
    
    @NotBlank(message = "ID cuộc trò chuyện không được để trống")
    private String conversationId;
    
    @NotBlank(message = "FCM token không được để trống")
    private String fcmToken;
}