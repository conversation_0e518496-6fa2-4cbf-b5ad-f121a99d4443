package org.dhv.pbl5server.firebase_service.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import org.dhv.pbl5server.common_service.utils.MapperUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class FirebaseConfiguration {
    private static final Logger logger = LoggerFactory.getLogger(FirebaseConfiguration.class);

    @Value("${firebase.type}")
    private String type;
    
    @Value("${firebase.project-id}")
    private String projectId;
    
    @Value("${firebase.private-key-id}")
    private String privateKeyId;
    
    @Value("${firebase.private-key}")
    private String privateKey;
    
    @Value("${firebase.client-email}")
    private String clientEmail;
    
    @Value("${firebase.client-id}")
    private String clientId;
    
    @Value("${firebase.auth-uri}")
    private String authUri;
    
    @Value("${firebase.token-uri}")
    private String tokenUri;
    
    @Value("${firebase.auth-provider-x509-cert-url}")
    private String authProviderX509CertUrl;
    
    @Value("${firebase.client-x509-cert-url}")
    private String clientX509CertUrl;
    
    @Value("${firebase.universe-domain}")
    private String universeDomain;

    @Bean
	FirebaseApp firebaseApp() throws IOException {
		try {
			logger.info("Initializing Firebase with projectId: {}", projectId);

			// Format private key
			String formattedPrivateKey = privateKey.replace("\\n", "\n");
			logger.debug("Formatted private key starts with: *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", app.getName());
				return app;
			} else {
				FirebaseApp app = FirebaseApp.getInstance();
				logger.info("Using existing FirebaseApp: {}", app.getName());
				return app;
			}
		} catch (Exception e) {
			logger.error("Failed to initialize FirebaseApp: {}", e.getMessage(), e);
			throw new RuntimeException("Firebase initialization failed", e);
		}
	}
} 