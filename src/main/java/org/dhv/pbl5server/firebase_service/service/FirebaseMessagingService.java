package org.dhv.pbl5server.firebase_service.service;

import com.google.firebase.messaging.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FirebaseMessagingService {

    /**
     * Gửi thông báo tin nhắn mới đến người dùng
     */
    public void sendNewMessageNotification(String recipientId, String senderId, 
                                           String senderName, String content, 
                                           String conversationId, String fcmToken) {
        try {
            if (fcmToken == null || fcmToken.isEmpty()) {
                log.warn("FCM token không tồn tại cho user: {}", recipientId);
                return;
            }
            
            Map<String, String> data = new HashMap<>();
            data.put("conversation_id", conversationId);
            data.put("sender_id", senderId);
            data.put("sender_name", senderName);
            data.put("type", "new_message");
            
            // Chuẩn bị nội dung hiển thị cho thông báo
            String notificationContent = content;
            if (content.length() > 100) {
                notificationContent = content.substring(0, 97) + "...";
            }
            
            // Tạo message
            Message message = Message.builder()
                .setNotification(Notification.builder()
                    .setTitle(senderName)
                    .setBody(notificationContent)
                    .build())
                .putAllData(data)
                .setToken(fcmToken)
                .setAndroidConfig(AndroidConfig.builder()
                    .setNotification(AndroidNotification.builder()
                        .setIcon("ic_notification")
                        .setColor("#FF9800")
                        .setChannelId("chat_channel")
                        .build())
                    .setPriority(AndroidConfig.Priority.HIGH)
                    .build())
                .setApnsConfig(ApnsConfig.builder()
                    .setAps(Aps.builder()
                        .setBadge(1)
                        .setSound("default")
                        .build())
                    .build())
                .build();
            
            // Gửi thông báo
            String response = FirebaseMessaging.getInstance().send(message);
            log.info("Successfully sent message to {}: {}", recipientId, response);
            
        } catch (FirebaseMessagingException e) {
            log.error("Failed to send Firebase notification to {}", recipientId, e);
        }
    }
    
    /**
     * Gửi thông báo đến nhiều thiết bị
     */
    public BatchResponse sendMulticastNotification(MulticastMessage message) {
        try {
            return FirebaseMessaging.getInstance().sendMulticast(message);
        } catch (FirebaseMessagingException e) {
            log.error("Failed to send multicast notification", e);
            return null;
        }
    }
    
    /**
     * Tạo notification khi có match mới
     */
    public void sendMatchNotification(String recipientId, String matchedWithName,
                                     String matchId, String fcmToken) {
        try {
            if (fcmToken == null || fcmToken.isEmpty()) {
                log.warn("FCM token không tồn tại cho user: {}", recipientId);
                return;
            }
            
            Map<String, String> data = new HashMap<>();
            data.put("match_id", matchId);
            data.put("type", "new_match");
            
            Message message = Message.builder()
                .setNotification(Notification.builder()
                    .setTitle("New Match!")
                    .setBody("You matched with " + matchedWithName)
                    .build())
                .putAllData(data)
                .setToken(fcmToken)
                .setAndroidConfig(AndroidConfig.builder()
                    .setNotification(AndroidNotification.builder()
                        .setIcon("ic_notification")
                        .setColor("#FF9800")
                        .setChannelId("match_channel")
                        .build())
                    .setPriority(AndroidConfig.Priority.HIGH)
                    .build())
                .build();
            
            String response = FirebaseMessaging.getInstance().send(message);
            log.info("Successfully sent match notification to {}: {}", recipientId, response);
            
        } catch (FirebaseMessagingException e) {
            log.error("Failed to send match notification to {}", recipientId, e);
        }
    }
} 