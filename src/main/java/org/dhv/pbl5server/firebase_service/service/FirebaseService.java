package org.dhv.pbl5server.firebase_service.service;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.constant_service.enums.SystemRoleName;
import org.dhv.pbl5server.profile_service.entity.Company;
import org.dhv.pbl5server.profile_service.entity.User;
import org.dhv.pbl5server.profile_service.repository.CompanyRepository;
import org.dhv.pbl5server.profile_service.repository.UserRepository;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class FirebaseService {

    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;

    /**
     * Tạo custom token cho Firebase Authentication
     */
    public String createCustomToken(String uid) throws FirebaseAuthException {
        return FirebaseAuth.getInstance().createCustomToken(uid);
    }
    
    /**
     * Tạo custom token với thêm claims
     */
    public String createCustomTokenWithClaims(String uid, Map<String, Object> additionalClaims) 
            throws FirebaseAuthException {
        return FirebaseAuth.getInstance().createCustomToken(uid, additionalClaims);
    }
    
    /**
     * Tạo hoặc cập nhật người dùng trong Firebase
     */
    public UserRecord createOrUpdateUser(Account account) throws FirebaseAuthException {
        String accountId = account.getAccountId().toString();
        String displayName = "";
        String photoUrl = account.getAvatar();
        
        // Get additional user info based on role
        if (account.getSystemRole().getConstantName().equals(SystemRoleName.USER.name())) {
            Optional<User> userOpt = userRepository.findById(account.getAccountId());
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                displayName = user.getFirstName() + " " + user.getLastName();
            }
        } else if (account.getSystemRole().getConstantName().equals(SystemRoleName.COMPANY.name())) {
            Optional<Company> companyOpt = companyRepository.findById(account.getAccountId());
            if (companyOpt.isPresent()) {
                Company company = companyOpt.get();
                displayName = company.getCompanyName();
            }
        }
        
        try {
            // Kiểm tra xem user đã tồn tại chưa
            UserRecord userRecord = FirebaseAuth.getInstance().getUser(accountId);
            
            // Cập nhật thông tin người dùng
            UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(accountId)
                    .setEmail(account.getEmail())
                    .setEmailVerified(true);
            
            if (displayName != null && !displayName.isEmpty()) {
                request.setDisplayName(displayName);
            }
            
            if (photoUrl != null && !photoUrl.isEmpty()) {
                request.setPhotoUrl(photoUrl);
            }
            
            return FirebaseAuth.getInstance().updateUser(request);
            
        } catch (FirebaseAuthException e) {
            // Nếu không tồn tại, tạo mới
            UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                    .setUid(accountId)
                    .setEmail(account.getEmail())
                    .setEmailVerified(true);
                    
            if (displayName != null && !displayName.isEmpty()) {
                request.setDisplayName(displayName);
            }
            
            if (photoUrl != null && !photoUrl.isEmpty()) {
                request.setPhotoUrl(photoUrl);
            }
                    
            return FirebaseAuth.getInstance().createUser(request);
        }
    }
    
    /**
     * Lưu token FCM vào thông tin user trong Firebase
     */
    public void saveFcmToken(String uid, String fcmToken) {
        try {
            Map<String, Object> claims = new HashMap<>();
            claims.put("fcm_token", fcmToken);
            
            FirebaseAuth.getInstance().setCustomUserClaims(uid, claims);
            log.info("FCM token saved for user: {}", uid);
        } catch (FirebaseAuthException e) {
            log.error("Error saving FCM token for user: {}", uid, e);
        }
    }
} 