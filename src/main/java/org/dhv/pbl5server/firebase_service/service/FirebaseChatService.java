package org.dhv.pbl5server.firebase_service.service;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.dhv.pbl5server.common_service.utils.LogUtils;
import org.dhv.pbl5server.firebase_service.model.ChatRoom;
import org.dhv.pbl5server.firebase_service.model.Message;
import org.dhv.pbl5server.firebase_service.model.MessageType;
import org.dhv.pbl5server.s3_service.service.S3Service;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.google.firebase.FirebaseApp;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.Query;
import com.google.firebase.database.ValueEventListener;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class FirebaseChatService {

	private DatabaseReference databaseReference;
	private final S3Service s3Service;
	private final FirebaseApp firebaseApp;
	
	@PostConstruct
	public void init() {
		try {
			LogUtils.info("Initializing FirebaseChatService with FirebaseApp: ", firebaseApp.getName());
			this.databaseReference = FirebaseDatabase.getInstance(firebaseApp).getReference();
			LogUtils.info("FirebaseDatabase reference initialized successfully", "");
		} catch (Exception e) {
			LogUtils.error("Failed to initialize FirebaseDatabase: {}", e.getMessage(), e);
			throw new RuntimeException("FirebaseDatabase initialization failed", e);
		}
	}
	
	// Create or get a chat room
	public CompletableFuture<ChatRoom> createOrGetChatRoom(List<String> participantIds) {
		String roomId = generateRoomId(participantIds);
		DatabaseReference roomRef = databaseReference.child("chat_rooms").child(roomId);

		CompletableFuture<ChatRoom> future = new CompletableFuture<>();
		roomRef.addListenerForSingleValueEvent(new ValueEventListener() {
			@Override
			public void onDataChange(DataSnapshot snapshot) {
				if (snapshot.exists()) {
					ChatRoom room = snapshot.getValue(ChatRoom.class);
					room.setRoomId(roomId);
					future.complete(room);
				} else {
					ChatRoom newRoom = new ChatRoom();
					newRoom.setRoomId(roomId);
					newRoom.setParticipantIds(participantIds);
					roomRef.setValueAsync(newRoom);
					future.complete(newRoom);
				}
			}

			@Override
			public void onCancelled(DatabaseError error) {
				LogUtils.error("Error creating/getting chat room: ", error.getMessage());
				future.completeExceptionally(error.toException());
			}
		});
		
		return future;
	}

	// Send a text message
	public CompletableFuture<Message> sendTextMessage(String roomId, String senderId, String content) {
		return sendMessage(roomId, senderId, content, MessageType.TEXT, null);
	}

	// Send a file or image message
	public CompletableFuture<Message> sendFileMessage(String roomId, String senderId, MultipartFile file) {
		String fileName = file.getOriginalFilename();
		MessageType messageType = this.determineMessageType(fileName);
		String storagePath = "chat_files/" + roomId; // folder

		// Upload file to Firebase Storage
		CompletableFuture<String> fileUrlFuture = CompletableFuture.supplyAsync(() -> {
			try {
				return this.s3Service.uploadFile(storagePath, file);
			} catch (Exception e) {
				LogUtils.error("Error uploading file to Firebase Storage: ", e.getMessage());
				throw new RuntimeException("Failed to upload file", e);
			}
		});

		// Send message with file URL
		return fileUrlFuture.thenCompose(fileUrl -> this.sendMessage(roomId, senderId, fileName, messageType, fileUrl));
	}

	// Helper method to send a message
	private CompletableFuture<Message> sendMessage(String roomId, String senderId, String content,
			MessageType messageType, String fileUrl) {
		DatabaseReference messagesRef = this.databaseReference.child("messages").child(roomId);
		String messageId = UUID.randomUUID().toString();
		Message message = new Message();
		message.setMessageId(messageId);
		message.setSenderId(senderId);
		message.setContent(content);
		message.setMessageType(messageType.name());
		message.setFileUrl(fileUrl);
		message.setTimestamp(OffsetDateTime.now().toString());

		CompletableFuture<Message> future = new CompletableFuture<>();
		messagesRef.child(messageId).setValue(message, (error, ref) -> {
			if (error != null) {
				LogUtils.error("Error sending message: ", error.getMessage());
				future.completeExceptionally(error.toException());
			} else {
				// Update last message in chat room
				this.databaseReference.child("chat_rooms").child(roomId).child("last_message").setValueAsync(message);
				future.complete(message);
			}
		});
		
		return future;
	}

	// Get message history for a chat room
	public CompletableFuture<List<Message>> getMessageHistory(String roomId) {
		DatabaseReference messagesRef = databaseReference.child("messages").child(roomId);
		Query query = messagesRef.orderByChild("timestamp");
		CompletableFuture<List<Message>> future = new CompletableFuture<>();

		query.addListenerForSingleValueEvent(new ValueEventListener() {
			@Override
			public void onDataChange(DataSnapshot snapshot) {
				List<Message> messages = new ArrayList<>();
				for (DataSnapshot messageSnapshot : snapshot.getChildren()) {
					Message message = messageSnapshot.getValue(Message.class);
					messages.add(message);
				}
				Collections.reverse(messages);
				future.complete(messages);
			}

			@Override
			public void onCancelled(DatabaseError error) {
				LogUtils.error("Error fetching message history: ", error.getMessage());
				future.completeExceptionally(error.toException());
			}
		});
		
		return future;
	}

	// Get a single chat room
	public CompletableFuture<ChatRoom> getChatRoom(String roomId) {
		DatabaseReference roomRef = databaseReference.child("chat_rooms").child(roomId);
		CompletableFuture<ChatRoom> future = new CompletableFuture<>();

		roomRef.addListenerForSingleValueEvent(new ValueEventListener() {
			@Override
			public void onDataChange(DataSnapshot snapshot) {
				if (snapshot.exists()) {
					ChatRoom room = snapshot.getValue(ChatRoom.class);
					room.setRoomId(roomId);
					future.complete(room);
				} else {
					future.completeExceptionally(new IllegalArgumentException("Chat room not found"));
				}
			}

			@Override
			public void onCancelled(DatabaseError error) {
				LogUtils.error("Error fetching chat room: ", error.getMessage());
				future.completeExceptionally(error.toException());
			}
		});

		return future;
	}

    // Get list of chat rooms for a user
    public CompletableFuture<List<ChatRoom>> getChatRooms(String userId) {
        DatabaseReference roomsRef = this.databaseReference.child("chat_rooms");
        CompletableFuture<List<ChatRoom>> future = new CompletableFuture<>();

        roomsRef.addListenerForSingleValueEvent(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot snapshot) {
                List<ChatRoom> rooms = new ArrayList<>();
                for (DataSnapshot roomSnapshot : snapshot.getChildren()) {
                    ChatRoom room = roomSnapshot.getValue(ChatRoom.class);
                    if (room.getParticipantIds().contains(userId)) {
                        room.setRoomId(roomSnapshot.getKey());
                        rooms.add(room);
                    }
                }
                future.complete(rooms);
            }

            @Override
            public void onCancelled(DatabaseError error) {
            	LogUtils.error("Error fetching chat rooms: ", error.getMessage());
                future.completeExceptionally(error.toException());
            }
        });
        
        return future;
    }

    // Generate a unique room ID based on participant IDs
	private String generateRoomId(List<String> participantIds) {
		var sortedIds = new ArrayList<>(participantIds);
		sortedIds.sort(String::compareTo);
		
		return String.join("_", sortedIds);
	}

	// Determine message type based on file extension
	private MessageType determineMessageType(String fileName) {
		if (Objects.isNull(fileName)) {
			return MessageType.TEXT;
		}
		
		var extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
		switch (extension) {
		case "jpg":
		case "jpeg":
		case "png":
			return MessageType.IMAGE;
		case "docx":
		case "pdf":
			return MessageType.FILE;
		default:
			return MessageType.FILE; // Default to FILE for unrecognized extensions
		}
	}
}
