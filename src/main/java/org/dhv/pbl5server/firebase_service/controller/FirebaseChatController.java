package org.dhv.pbl5server.firebase_service.controller;

import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.lang3.StringUtils;
import org.dhv.pbl5server.authentication_service.annotation.CurrentAccount;
import org.dhv.pbl5server.authentication_service.annotation.PreAuthorizeSystemRole;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.constant.ErrorMessageConstant;
import org.dhv.pbl5server.common_service.exception.BadRequestException;
import org.dhv.pbl5server.common_service.exception.UnauthorizedException;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.dhv.pbl5server.firebase_service.model.Message;
import org.dhv.pbl5server.firebase_service.payload.MessageNotificationRequest;
import org.dhv.pbl5server.firebase_service.service.FirebaseChatService;
import org.dhv.pbl5server.firebase_service.service.FirebaseMessagingService;
import org.dhv.pbl5server.profile_service.service.CompanyService;
import org.dhv.pbl5server.profile_service.service.UserService;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

/**
 * Controller xử lý thông báo tin nhắn chat qua FCM
 */
@RestController
@RequestMapping("/v1/firebase/chat")
@RequiredArgsConstructor
public class FirebaseChatController {

	private final FirebaseChatService firebaseChatService;
    private final FirebaseMessagingService firebaseMessagingService;
    private final UserService userService;
    private final CompanyService companyService;

    /**
     * Endpoint để gửi thông báo tin nhắn mới
     */
    @PreAuthorizeSystemRole
    @PostMapping("/send-message-notification")
    public ResponseEntity<ApiDataResponse> sendMessageNotification(
            @CurrentAccount Account currentAccount,
            @Valid @RequestBody MessageNotificationRequest request) {
        
        firebaseMessagingService.sendNewMessageNotification(
            request.getRecipientId(),
            currentAccount.getAccountId().toString(),
            request.getSenderName(),
            request.getMessageContent(),
            request.getConversationId(),
            request.getFcmToken()
        );
        
        return ResponseEntity.ok(ApiDataResponse.successWithoutMetaAndData());
    }
    
	/**
	 * Create or get a chat room
	 */
	@PreAuthorizeSystemRole
	@PostMapping(path = "/room")
	public ResponseEntity<ApiDataResponse> createChatRoom(
			@CurrentAccount Account currentAccount,
			@RequestBody List<String> participantIds) {
		if (!participantIds.contains(currentAccount.getAccountId().toString())) {
			participantIds.add(currentAccount.getAccountId().toString());
		}

		return this.firebaseChatService.createOrGetChatRoom(participantIds)
				.thenApply(room -> ResponseEntity.ok(ApiDataResponse.successWithoutMeta(room)))
				.join();
	}

	/**
	 * Send a message (text or file)
	 */
	@PreAuthorizeSystemRole
	@PostMapping(value = "/message", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseEntity<ApiDataResponse> sendMessage(
			@RequestParam(name = "conversation_id", required = true) String roomId,
			@CurrentAccount Account currentAccount,
			@RequestPart(value = "file", required = false) MultipartFile file,
			@RequestPart(value = "message_content", required = false) String messageContent,
			@RequestPart(value = "fcm_token", required = true) String fcmToken) {
		if (StringUtils.isBlank(roomId)) {
			throw new BadRequestException(ErrorMessageConstant.MESSAGE_MUST_HAVE_CONTENT_OR_FILE);
		}

		String senderId = currentAccount.getAccountId().toString();
		CompletableFuture<Message> messageFuture;
		if (Objects.nonNull(file) && !file.isEmpty()) { // Handle file/image message
			messageFuture = this.firebaseChatService.sendFileMessage(roomId, senderId, file);
		} else if (!StringUtils.isBlank(messageContent)){ // Handle text message
			messageFuture = this.firebaseChatService.sendTextMessage(roomId, senderId, messageContent);
		} else {
			throw new BadRequestException(ErrorMessageConstant.MESSAGE_MUST_HAVE_CONTENT_OR_FILE);
		}

		return messageFuture.thenCompose(message -> {
			// extract recipientId from room participants
			List<String> participantIds = Arrays.stream(roomId.split("_"))
					.filter(id -> !id.equals(senderId))
					.toList();

			String senderName = switch (currentAccount.getSystemRole().getConstantName()) {
			case "User" -> this.userService.findById(senderId).getFullName();
			case "Company" -> this.companyService.findById(senderId).getCompanyName();
			default -> "Unknown Sender";
			};
			
			// Send notification
			participantIds.forEach(receiptId -> this.firebaseMessagingService.sendNewMessageNotification(
					receiptId, senderId, senderName, message.getContent(), roomId, fcmToken));
			return CompletableFuture.completedFuture(ResponseEntity.ok(ApiDataResponse.successWithoutMeta(message)));
		}).join();
	}

	/**
	 * Get message history for a chat room
	 */
	@PreAuthorizeSystemRole
	@GetMapping(path = "/messages")
	public ResponseEntity<ApiDataResponse> getMessageHistory(
			@CurrentAccount Account currentAccount,
			@RequestParam String conversationId) {
		
		return this.firebaseChatService.getChatRoom(conversationId).thenCompose(room -> {
			if (!room.getParticipantIds().contains(currentAccount.getAccountId().toString())) {
				throw new UnauthorizedException("User not authorized for this chat room!");
			}
			
			return this.firebaseChatService.getMessageHistory(conversationId)
					.thenApply(messages -> ResponseEntity.ok(ApiDataResponse.successWithoutMeta(messages)));
		}).join();
	}

	/**
	 * Get list of chat rooms for the current user
	 */
	@PreAuthorizeSystemRole
	@GetMapping("/rooms")
	public ResponseEntity<ApiDataResponse> getChatRooms(@CurrentAccount Account currentAccount) {
		return this.firebaseChatService.getChatRooms(currentAccount.getAccountId().toString())
				.thenApply(rooms -> ResponseEntity.ok(ApiDataResponse.successWithoutMeta(rooms)))
				.join();
	}
}