package org.dhv.pbl5server.firebase_service.controller;

import lombok.RequiredArgsConstructor;
import org.dhv.pbl5server.authentication_service.annotation.CurrentAccount;
import org.dhv.pbl5server.authentication_service.annotation.PreAuthorizeSystemRole;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.dhv.pbl5server.firebase_service.payload.MatchNotificationRequest;
import org.dhv.pbl5server.firebase_service.service.FirebaseMessagingService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * Controller xử lý thông báo match qua FCM
 */
@RestController
@RequestMapping("/v1/firebase/match")
@RequiredArgsConstructor
public class FirebaseMatchingController {

    private final FirebaseMessagingService firebaseMessagingService;

    /**
     * Endpoint để gửi thông báo có match mới
     */
    @PreAuthorizeSystemRole
    @PostMapping("/send-match-notification")
    public ResponseEntity<ApiDataResponse> sendMatchNotification(
            @CurrentAccount Account currentAccount,
            @Valid @RequestBody MatchNotificationRequest request) {
        
        firebaseMessagingService.sendMatchNotification(
            request.getRecipientId(),
            request.getMatchedWithName(),
            request.getMatchId(),
            request.getFcmToken()
        );
        
        return ResponseEntity.ok(ApiDataResponse.successWithoutMetaAndData());
    }
}