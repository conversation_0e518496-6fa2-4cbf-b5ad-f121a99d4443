package org.dhv.pbl5server.firebase_service.controller;

import lombok.RequiredArgsConstructor;
import org.dhv.pbl5server.authentication_service.annotation.CurrentAccount;
import org.dhv.pbl5server.authentication_service.annotation.PreAuthorizeSystemRole;
import org.dhv.pbl5server.authentication_service.entity.Account;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.dhv.pbl5server.firebase_service.payload.FCMTokenRequest;
import org.dhv.pbl5server.firebase_service.service.FirebaseService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/v1/firebase")
@RequiredArgsConstructor
public class FirebaseController {

    private final FirebaseService firebaseService;

    /**
     * Endpoint để cập nhật FCM token của người dùng
     */
    @PreAuthorizeSystemRole
    @PostMapping("/register-fcm-token")
    public ResponseEntity<ApiDataResponse> registerFCMToken(
            @CurrentAccount Account currentAccount,
            @Valid @RequestBody FCMTokenRequest request) {
        
        // Lưu token FCM vào thông tin user trong Firebase
        firebaseService.saveFcmToken(currentAccount.getAccountId().toString(), request.getFcmToken());
        
        return ResponseEntity.ok(ApiDataResponse.successWithoutMetaAndData());
    }
} 