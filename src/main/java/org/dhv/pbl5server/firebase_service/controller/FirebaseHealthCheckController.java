package org.dhv.pbl5server.firebase_service.controller;

import com.google.firebase.FirebaseApp;
import lombok.RequiredArgsConstructor;
import org.dhv.pbl5server.common_service.model.ApiDataResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller kiểm tra kết nối Firebase
 */
@RestController
@RequestMapping("/v1/firebase/health")
@RequiredArgsConstructor
public class FirebaseHealthCheckController {

    private final FirebaseApp firebaseApp;

    /**
     * Endpoint kiểm tra kết nối Firebase
     */
    @GetMapping("/check")
    public ResponseEntity<ApiDataResponse> checkFirebaseConnection() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("connection", "success");
            status.put("app_name", firebaseApp.getName());
            status.put("project_id", firebaseApp.getOptions().getProjectId());
            
            return ResponseEntity.ok(ApiDataResponse.successWithoutMeta(status));
        } catch (Exception e) {
            status.put("connection", "failed");
            status.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(ApiDataResponse.successWithoutMeta(status));
        }
    }
} 