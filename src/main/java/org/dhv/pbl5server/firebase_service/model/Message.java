package org.dhv.pbl5server.firebase_service.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class Message {

	@JsonProperty(value = "message_id")
	private String messageId;

	@JsonProperty(value = "sender_id")
	private String senderId;

	@JsonProperty(value = "content")
	private String content; // Text content or file name for FILE/IMAGE

	@JsonProperty(value = "message_type")
	private String messageType; // TEXT, FILE, IMAGE

	@JsonProperty(value = "file_url")
	private String fileUrl;

	@JsonProperty(value = "timestamp")
	private String timestamp;
}
