package org.dhv.pbl5server;

import org.dhv.pbl5server.authentication_service.config.JwtApplicationProperty;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.retry.annotation.EnableRetry;

@EnableConfigurationProperties({JwtApplicationProperty.class})
@SpringBootApplication
@EnableRetry
public class Pbl5ServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(Pbl5ServerApplication.class, args);
    }
}
