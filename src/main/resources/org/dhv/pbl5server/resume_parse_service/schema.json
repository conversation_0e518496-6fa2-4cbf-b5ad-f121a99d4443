{"data_schema": {"additionalProperties": false, "properties": {"basic_info": {"additionalProperties": false, "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string"}, "phone_number": {"type": "string"}, "address": {"type": "string"}, "date_of_birth": {"type": "string"}, "gender": {"type": "boolean"}, "summary_introduction": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "social_media_links": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}]}}, "required": ["first_name", "last_name", "email", "phone_number", "address", "date_of_birth", "gender", "summary_introduction", "social_media_links"], "type": "object"}, "education": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"study_place": {"type": "string"}, "study_start_time": {"type": "string"}, "study_end_time": {"type": "string"}, "majority": {"type": "string"}, "cpa": {"type": "number"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "is_university": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}}, "required": ["study_place", "study_start_time", "study_end_time", "majority", "cpa", "description", "is_university"], "type": "object"}, "type": "array"}, {"type": "null"}]}, "experiences": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"work_place": {"type": "string"}, "position": {"type": "string"}, "experience_start_time": {"type": "string"}, "experience_end_time": {"type": "string"}, "experience_title": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}]}}, "required": ["work_place", "position", "experience_start_time", "experience_end_time", "experience_title", "description"], "type": "object"}, "type": "array"}, {"type": "null"}]}, "awards": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"certificate_name": {"type": "string"}, "certificate_time": {"type": "string"}, "description": {"type": "string"}}, "required": ["certificate_name", "certificate_time", "description"], "type": "object"}, "type": "array"}, {"type": "null"}]}, "languages": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"language_name": {"type": "string"}, "language_score": {"type": "string"}, "language_certificate_name": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "language_certificate_date": {"anyOf": [{"type": "string"}, {"type": "null"}]}}, "required": ["language_name", "language_score", "language_certificate_name", "language_certificate_date"], "type": "object"}, "type": "array"}, {"type": "null"}]}, "skills": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"skill_name": {"type": "string"}, "skill_level": {"anyOf": [{"type": "string"}, {"type": "null"}]}}, "required": ["skill_name", "skill_level"], "type": "object"}, "type": "array"}, {"type": "null"}]}, "others": {"anyOf": [{"items": {"additionalProperties": false, "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "type": "object"}, "type": "array"}, {"type": "null"}]}}, "required": ["basic_info", "education", "experiences", "awards", "languages", "skills", "others"], "type": "object"}, "config": {"extraction_target": "PER_PAGE", "extraction_mode": "BALANCED", "system_prompt": "Extract the following information from the provided resume text and format it according to the specified schema:\n\nRequired fields (MUST NOT be null or empty):\n- Basic Info:\n  * first_name (string)\n  * last_name (string)\n  * email (string)\n  * phone_number (string)\n  * address (string)\n  * date_of_birth (timestamp)\n  * gender (boolean: true=male, false=female)\n  * account_status (boolean: default to true)\n\n- Education (for each entry):\n  * study_place (string)\n  * study_start_time (timestamp)\n  * study_end_time (timestamp) \n  * majority (string)\n  * cpa (number)\n\n- Experience (for each entry):\n  * work_place (string)\n  * position (string)\n  * experience_start_time (timestamp)\n  * experience_end_time (timestamp)\n\n- Language (for each entry):\n  * language_name (string)\n  * language_score (string)\n\nOptional fields (can be null):\n- Basic Info:\n  * summary_introduction (string)\n  * social_media_links (string array)\n\n- Education:\n  * description (string)\n  * is_university (boolean)\n\n- Experience:\n  * description (string)\n\n- Language:\n  * language_certificate_name (string)\n  * language_certificate_date (timestamp)\n\n- Awards (array):\n  * certificate_name (string)\n  * certificate_time (timestamp)\n  * description (string)\n\n- Others (array):\n  * title (string)\n  * description (string)\n\nFormatting rules:\n1. All timestamps must be in ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ\n2. All required fields must have valid non-null values\n3. Optional fields can be null\n4. Empty arrays should be represented as []\n5. All string values should be trimmed and cleaned of special characters\n6. Numbers (like CPA) should be valid decimal values\n\n\nExtract key information from the resume with special attention to the following:\n\nSkills Extraction Guidelines:\n1. Identify Core Skills:\n   - Look for explicitly stated skills sections\n   - Extract skills mentioned in work experiences\n   - Note skills demonstrated in projects/achievements\n   - Include both hard and soft skills\n\n2. Determine Key Skills by:\n   - Frequency of mention throughout the resume\n   - Emphasis in job descriptions\n   - Relevance to career achievements\n   - Skills highlighted in summary/objective\n   - Skills backed by certifications/training\n\n3. Categorize Skills:\n   - Technical/Professional Skills (specific to industry)\n   - Soft Skills (leadership, communication, etc.)\n   - Tools/Software Proficiency\n   - Industry-specific Knowledge\n   - Languages (if relevant)\n\n4. Assess Skill Level based on:\n   - Years of experience\n   - Context of usage\n   - Achievements related to the skill\n   - Certifications/Training level\n   Levels: \"Beginner\", \"Intermediate\", \"Advanced\", \"Expert\"\n\nProfessional Online Presence:\n1. Look for professional links such as:\n   - Professional networking profiles\n   - Portfolio/Personal websites\n   - Professional blogs\n   - Industry-specific platforms\n   - Work showcases\n   - Professional social media presence\n\n2. URL Guidelines:\n   - Must be complete URLs (including https://)\n   - Should be professional in nature\n   - Must be relevant to career/profession\n   - Check for any mentioned professional handles/usernames\n\nNote: Focus on extracting skills that demonstrate expertise and relevance to the person's career path, regardless of industry.\n\nLanguage Information Guidelines:\n1. For language_score:\n   - ONLY use for numeric test scores (e.g., \"7.0\", \"850\", \"100\")\n   - Leave null if only certificate level is provided\n   Examples:\n   - IELTS 7.0 -> language_score: \"7.0\"\n   - TOEIC 850 -> language_score: \"850\"\n   - JLPT N2 -> language_score: null\n\n2. For language_certificate_name:\n   - Use for certificate names and levels\n   - Include both certificate type and level if available\n   Examples:\n   - \"IELTS Academic\"\n   - \"TOEIC Listening and Reading\"\n   - \"JLPT N2\"\n   - \"DELF B2\"\n\nExamples:\nCase 1 - With test score:\n{\n  \"language_name\": \"English\",\n  \"language_score\": \"7.0\",\n  \"language_certificate_name\": \"IELTS Academic\"\n}\n\nCase 2 - Certificate level only:\n{\n  \"language_name\": \"Japanese\",\n  \"language_score\": null,\n  \"language_certificate_name\": \"JLPT N2\"\n}\n\n[Previous Skills and Professional Online Presence sections remain the same...]\n\nAdditional Formatting Notes:\n1. Language scores should only contain numeric values\n2. Certificate names should be standardized and well-known credentials\n3. Dates must follow ISO 8601 format\n4. All string values should be properly capitalized\n", "use_reasoning": true, "cite_sources": false}}