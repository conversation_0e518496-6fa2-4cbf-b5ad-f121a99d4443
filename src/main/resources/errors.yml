# Common
internal_server_error:
  code: ERR_SER0101
  message: Internal server error!
bad_request_error:
  code: ERR_SER0102
  message: Bad request error!
page_not_found:
  code: ERR_SER0103
  message: Page was not found!
forbidden:
  code: ERR_AUTH0101
  message: Access Denied! You don’t have permission to access!
unauthorized:
  code: ERR_AUTH0102
  message: You do not have permission to access this data!
required_body_in_request:
  code: ERR_REQ0101
  message: The request body is required!
invalid_uuid:
  code: ERR_REQ0102
  message: Invalid UUID!
invalid_enum:
  code: ERR_REQ0103
  message: Invalid enum!

# Authentication
invalid_token:
  code: ERR_TOK0101
  message: Invalid token!
expired_token:
  code: ERR_TOK0102
  message: Expired token!
revoked_token:
  code: ERR_TOK0103
  message: Revoked token!
invalid_refresh_token:
  code: ERR_TOK0201
  message: Invalid refresh token!
expired_refresh_token:
  code: ERR_TOK0202
  message: Expired refresh token!
revoked_refresh_token:
  code: ERR_TOK0203
  message: Revoked refresh token!
refresh_token_not_found:
  code: ERR_TOK0204
  message: Refresh token is not found!
reset_password_code_invalid:
  code: ERR_FOR0101
  message: Reset password code is invalid!
reset_password_code_expired:
  code: ERR_FOR0102
  message: Reset password code is expired!

# Permission
permission_not_found:
  code: ERR_PER0102
  message: Permission not found!
permission_is_require:
  code: ERR_PER0103
  message: Permission is require!

# Account
account_not_found:
  code: ERR_ACC0101
  message: Account is not found!
incorrect_email_or_password:
  code: ERR_ACC0103
  message: Incorrect email or password!
login_failed:
  code: ERR_ACC0104
  message: Login failed!
account_is_disabled:
  code: ERR_ACC0105
  message: Account is disabled!
account_is_active:
  code: ERR_ACC0106
  message: Account is active!
account_is_not_active:
  code: ERR_ACC0107
  message: Account is not active!
account_id_is_required:
  code: ERR_ACC0108
  message: Account id is required!

# Constant
constant_not_found:
  code: ERR_CON0101
  message: Constant is not found!
system_role_not_found:
  code: ERR_CON0102
  message: System role is not found!
constant_type_must_be_number:
  code: ERR_CON0103
  message: Constant type must be a number!
constant_type_must_be_system_role:
  code: ERR_CON0104
  message: Constant type must be a system role!
constant_type_must_be_apply_position:
  code: ERR_CON0105
  message: Constant type must be an apply position!
constant_type_must_be_apply_skill:
  code: ERR_CON0106
  message: Constant type must be an apply skill!
constant_type_must_be_experience:
  code: ERR_CON0107
  message: Constant type must be an experience!
constant_type_must_be_notification:
  code: ERR_CON0108
  message: Constant type must be a notification!
constant_type_must_be_language:
  code: ERR_CON0109
  message: Constant type must be a language!
constant_type_must_be_salary_range:
  code: ERR_CON0110
  message: Constant type must be a salary range!


# Register
email_already_exists:
  code: ERR_REG0101
  message: Email already exists!
role_not_valid:
  code: ERR_REG0102
  message: Role is not valid! [COMPANY or USER]

# Reset & change password
new_password_same_old_password:
  code: ERR_RST0101
  message: New password is the same as the old password!
new_password_confirmation_not_match:
  code: ERR_RST0102
  message: New password confirmation does not match!
current_password_is_incorrect:
  code: ERR_RST0103
  message: Current password is incorrect!

# File
upload_file_failed:
  code: ERR_FIL0101
  message: Upload file failed!
file_not_found:
  code: ERR_FIL0102
  message: File not found!
delete_file_failed:
  code: ERR_FIL0103
  message: Delete file failed!

# User
user_not_found:
  code: ERR_USE0101
  message: User is not found!
basic_info_request_must_be_object:
  code: ERR_USE0102
  message: Basic info request must be an object!
basic_info_request_invalid:
  code: ERR_USE0103
  message: Basic info request is invalid!
component_id_is_required:
  code: ERR_USE0104
  message: With user profile component, component_id is required!

# Education
study_end_time_must_be_greater_than_start_time:
  code: ERR_EDU0101
  message: Study end time must be greater than start time!
education_request_must_be_list:
  code: ERR_EDU0102
  message: Education request must be a list!
education_request_invalid:
  code: ERR_EDU0103
  message: Education request is invalid!
education_not_found:
  code: ERR_EDU0104
  message: Education not found!
education_id_is_required:
  code: ERR_EDU0105
  message: Education id is required!

# Award
award_request_must_be_list:
  code: ERR_AWA0101
  message: Award request must be a list!
award_request_invalid:
  code: ERR_AWA0102
  message: Award request is invalid!
award_not_found:
  code: ERR_AWA0103
  message: Award not found!
award_id_is_required:
  code: ERR_AWA0104
  message: Award id is required!

# Experience
experience_request_must_be_list:
  code: ERR_EXP0101
  message: Experience request must be a list!
experience_request_invalid:
  code: ERR_EXP0102
  message: Experience request is invalid!
experience_end_time_must_be_greater_than_start_time:
  code: ERR_EXP0101
  message: Experience end time must be greater than start time!
experience_not_found:
  code: ERR_EXP0102
  message: Experience not found!
experience_id_is_required:
  code: ERR_EXP0103
  message: Experience id is required!

# OtherDescription
other_description_request_must_be_list:
  code: ERR_ODS0101
  message: Other description request must be a list!
other_description_request_invalid:
  code: ERR_ODS0102
  message: Other description request is invalid!
other_description_not_found:
  code: ERR_ODS0103
  message: Other description not found!
other_description_user_id_is_required:
  code: ERR_ODS0104
  message: With other description, user id is required!
other_description_id_is_required:
  code: ERR_ODS0105
  message: Other description id is required!

# Delete[Award|Education|OtherDescription|Experience]
delete_ids_request_have_one_not_found:
  code: ERR_DEL0101
  message: Delete ids request have one id which is not found!

# ApplicationPosition
application_position_not_found:
  code: ERR_APP0101
  message: Application position not found!
application_position_id_is_required:
  code: ERR_APP0102
  message: Application position id is required!

# ApplicationSkill
application_skill_not_found:
  code: ERR_APP0201
  message: Application skill not found!
application_skill_id_is_required:
  code: ERR_APP0202
  message: Application skill id is required!

# CompanyProfile
company_profile_not_found:
  code: ERR_COM0101
  message: Company profile not found!

# Conversation
conversation_not_found:
  code: ERR_CON0101
  message: Conversation not found!
user_and_company_are_required:
  code: ERR_CON0102
  message: User and company are required!

# Message
message_not_found:
  code: ERR_MSG0101
  message: Message not found!
message_must_have_content_or_file:
  code: ERR_MSG0102
  message: Content or files must be provided!

# Notification
notification_not_found:
  code: ERR_NOT0101
  message: Notification not found!
required_sender_and_receiver:
  code: ERR_NOT0102
  message: Sender and receiver are required!
required_notification_type:
  code: ERR_NOT0103
  message: Notification type is required!

# Match
match_not_found:
  code: ERR_MAT0101
  message: Match is not found!
match_id_is_invalid:
  code: ERR_MAT0102
  message: Match id is required and must be a valid UUID!
requested_account_id_is_invalid:
  code: ERR_MAT0103
  message: Requested account id is required and must be a valid UUID!
requested_account_not_found:
  code: ERR_MAT0104
  message: Requested account is not found!
requested_account_and_your_account_must_not_same_role:
  code: ERR_MAT0105
  message: Requested account and your account must not have the same role!
requested_account_is_banned:
  code: ERR_MAT0106
  message: Requested account is banned!
match_already_matched:
  code: ERR_MAT0107
  message: You have already matched with this account!
match_not_accepted_by_yourself:
  code: ERR_MAT0108
  message: You can not accept the match by yourself!
reject_not_accepted_match:
  code: ERR_MAT0109
  message: You can not reject the not accepted match!
match_feature_not_for_admin:
  code: ERR_MAT0110
  message: Match feature is not for admin!
match_already_requested:
  code: ERR_MAT0111
  message: You have already requested to match with this account!
match_already_rejected:
  code: ERR_MAT0112
  message: You have already rejected the match with this account!
match_already_cancelled:
  code: ERR_MAT0113
  message: You have already cancelled the match with this account!
match_not_accepted:
  code: ERR_MAT0114
  message: Match is not accepted!
interview_time_invalid:
  code: ERR_MAT0115
  message: Interview time must be greater than current time!
sent_interview_invitation_mail_exceed:
  code: ERR_MAT0116
  message: You have exceeded the number of sent interview invitation emails!
dont_have_any_match_between_two_accounts:
  code: ERR_MAT0117
  message: Don't have any match between two accounts!

# Language
language_not_found:
  code: ERR_LAN0101
  message: Language not found!
language_score_invalid:
  code: ERR_LAN0102
  message: Language score is invalid!
language_id_is_required:
  code: ERR_LAN0103
  message: Language id is required!

# Resume Parse
resume_parse_failed:
  code: ERR_RES0101
  message: Resume parsing failed!
resume_file_upload_failed:
  code: ERR_RES0102
  message: Resume file upload failed!
extraction_job_creation_failed:
  code: ERR_RES0103
  message: Extraction job creation failed!
extraction_job_failed:
  code: ERR_RES0104
  message: Extraction job failed or timed out!
extraction_result_failed:
  code: ERR_RES0105
  message: Failed to get extraction results!
