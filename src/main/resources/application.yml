server:
  port: 8080
  servlet:
    context-path: /api

spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    database: postgresql
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  cache:
    type: redis
    redis:
      time-to-live: 6000
  data:
    redis:
      host: ${SPRING_REDIS_HOST}
      port: ${SPRING_REDIS_PORT}
      username: ${SPRING_REDIS_USERNAME}
      password: ${SPRING_REDIS_PASSWORD}
      ssl:
        enabled: ${SPRING_REDIS_SSL_ENABLED}
      jedis:
        pool:
          enabled: true
          max-active: 16
          max-idle: 16
          min-idle: 8
          max-wait: 1800 # 30 minutes
  mail:
    default-encoding: utf-8
    host: ${SPRING_MAIL_HOST}
    port: ${SPRING_MAIL_PORT}
    username: ${SPRING_MAIL_USERNAME}
    password: ${SPRING_MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    protocol: smtp
    test-connection: false

logging:
  level:
    org.springframework.security: trace
  file:
    name: log/dev.log

application:
  profile: ${SPRING_PROFILE}
  auth:
    access-token-secret-key: ${JWT_ACCESS_TOKEN_SECRET_KEY}
    access-token-expiration-ms: ${JWT_ACCESS_TOKEN_EXPIRATION_MS}
    refresh-token-secret-key: ${JWT_REFRESH_TOKEN_SECRET_KEY}
    refresh-token-expiration-ms: ${JWT_REFRESH_TOKEN_EXPIRATION_MS}
    reset-password-code-expiration-ms: ${RESET_PASSWORD_CODE_EXPIRATION_MS}
  server.socket:
    port: 8888
  s3:
    bucket-name: ${S3_BUCKET_NAME}
    access-key: ${S3_ACCESS_KEY}
    secret-key: ${S3_SECRET_KEY}
  llama-index:
    api-url: https://api.cloud.llamaindex.ai/api/v1
    api-key: ${LLAMA_INDEX_API_KEY}
    agent-id: ${LLAMA_INDEX_AGENT_ID}
    project-id: ${LLAMA_INDEX_PROJECT_ID:}
  max-sent-interview-invitation-mail-per-day-and-user: ${MAX_SENT_INTERVIEW_INVITATION_MAIL_PER_DAY_AND_USER}

# Thêm cấu hình Firebase từ biến môi trường
firebase:
  type: ${FIREBASE_TYPE}
  project-id: ${FIREBASE_PROJECT_ID}
  private-key-id: ${FIREBASE_PRIVATE_KEY_ID}
  private-key: ${FIREBASE_PRIVATE_KEY}
  client-email: ${FIREBASE_CLIENT_EMAIL}
  client-id: ${FIREBASE_CLIENT_ID}
  auth-uri: ${FIREBASE_AUTH_URI}
  token-uri: ${FIREBASE_TOKEN_URI}
  auth-provider-x509-cert-url: ${FIREBASE_AUTH_PROVIDER_X509_CERT_URL}
  client-x509-cert-url: ${FIREBASE_CLIENT_X509_CERT_URL}
  universe-domain: ${FIREBASE_UNIVERSE_DOMAIN}

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    operations-sorter: method
