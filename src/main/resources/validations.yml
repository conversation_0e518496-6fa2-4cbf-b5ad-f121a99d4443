# LoginRequest
login_request:
  email:
    not_blank:
      code: VAL_LOG0101
      message: Email cannot be blank
    email:
      code: VAL_LOG0102
      message: Email must be a well-formed email address
  password:
    password_validation:
      code: VAL_LOG0103
      message: Password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.

# RefreshTokenRequest
refresh_token_request:
  refresh_token:
    not_blank_string_validation:
      code: VAL_REF0101
      message: Refresh token cannot be blank

# ConstantSelectionRequest
constant_selection_request:
  constant_id:
    uuid_validation:
      code: VAL_SEL0101
      message: Constant ID must be a valid UUID

# ConstantRequest
constant_request:
  constant_prefix:
    not_blank_string_validation:
      code: VAL_CON0101
      message: Constant prefix cannot be blank
    pattern:
      code: VAL_CON0102
      message: Constant prefix must be in the format of \\d{2}
  constant_name:
    not_blank_string_validation:
      code: VAL_CON0103
      message: Constant name cannot be blank

# UserRegisterRequest
user_register_request:
  email:
    email:
      code: VAL_REG0101
      message: Email must be a well-formed email address
    not_blank:
      code: VAL_REG0102
      message: Email cannot be blank
  password:
    password_validation:
      code: VAL_REG0103
      message: Password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.
  address:
    not_blank_string_validation:
      code: VAL_REG0104
      message: Address cannot be blank
  phone_number:
    not_blank_string_validation:
      code: VAL_REG0105
      message: Phone number cannot be blank
  system_role:
    not_null:
      code: VAL_REG0106
      message: System role cannot be null
  date_of_birth:
    not_null:
      code: VAL_REG0107
      message: Date of birth cannot be null
  first_name:
    not_blank_string_validation:
      code: VAL_REG0108
      message: First name cannot be blank
  last_name:
    not_blank_string_validation:
      code: VAL_REG0109
      message: Last name cannot be blank
  gender:
    not_null:
      code: VAL_REG0110
      message: Gender cannot be null
  system_role.constant_id:
    uuid_validation:
      code: VAL_REG0111
      message: System role constant ID must be a valid UUID

# CompanyRegisterRequest
company_register_request:
  email:
    email:
      code: VAL_REG0201
      message: Email must be a well-formed email address
    not_blank:
      code: VAL_REG0202
      message: Email cannot be blank
  password:
    password_validation:
      code: VAL_REG0203
      message: Password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.
  address:
    not_blank_string_validation:
      code: VAL_REG0204
      message: Address cannot be blank
  phone_number:
    not_blank_string_validation:
      code: VAL_REG0205
      message: Phone number cannot be blank
  system_role:
    not_null:
      code: VAL_REG0206
      message: System role cannot be null
  company_name:
    not_blank_string_validation:
      code: VAL_REG0207
      message: Company name cannot be blank
  company_url:
    not_blank_string_validation:
      code: VAL_REG0208
      message: Company URL cannot be blank
  established_date:
    not_null:
      code: VAL_REG0209
      message: Established date cannot be null
  system_role.constant_id:
    uuid_validation:
      code: VAL_REG0210
      message: System role constant ID must be a valid UUID

# ForgotPasswordRequest
forgot_password_request:
  email:
    email:
      code: VAL_FOR0101
      message: Email must be a well-formed email address
    not_blank:
      code: VAL_FOR0102
      message: Email cannot be blank

# ResetPasswordRequest
reset_password_request:
  reset_password_code:
    not_null:
      code: VAL_RES0101
      message: Reset password code cannot be null
    pattern:
      code: VAL_RES0102
      message: Reset password code must be numeric with 6 digits
  email:
    not_null:
      code: VAL_RES0103
      message: Email cannot be null
    email:
      code: VAL_RES0104
      message: Email must be a well-formed email address
  new_password:
    password_validation:
      code: VAL_RES0105
      message: New password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.
  new_password_confirmation:
    password_validation:
      code: VAL_RES0106
      message: New password confirmation is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.

# ChangePasswordRequest
change_password_request:
  current_password:
    password_validation:
      code: VAL_CHA0101
      message: Current password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.
  new_password:
    password_validation:
      code: VAL_CHA0102
      message: New password is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.
  new_password_confirmation:
    password_validation:
      code: VAL_CHA0103
      message: New password confirmation is required. It must be between 8 and 32 characters, containing at least 1 uppercase letter, 1 lowercase letter, 1 numeric character.

# UserBasicInfoRequest
user_basic_info_request:
  first_name:
    not_blank_string_validation:
      code: VAL_BAS0101
      message: First name cannot be blank
  last_name:
    not_blank_string_validation:
      code: VAL_BAS0102
      message: Last name cannot be blank
  account_status:
    not_null:
      code: VAL_BAS0103
      message: Account status cannot be null
  address:
    not_blank_string_validation:
      code: VAL_BAS0104
      message: Address cannot be blank
  phone_number:
    not_blank_string_validation:
      code: VAL_BAS0105
      message: Phone number cannot be blanks
  gender:
    not_null:
      code: VAL_BAS0106
      message: Gender cannot be null
  date_of_birth:
    not_null:
      code: VAL_BAS0107
      message: Date of birth cannot be null

# UserEducationRequest
user_education_request:
  study_place:
    not_blank_string_validation:
      code: VAL_EDU0101
      message: Study place cannot be blank
  study_start_time:
    not_null:
      code: VAL_EDU0102
      message: Study start time cannot be null
  study_end_time:
    not_null:
      code: VAL_EDU0103
      message: Study end time cannot be null
  majority:
    not_blank_string_validation:
      code: VAL_EDU0104
      message: Majority cannot be blank
  cpa:
    not_null:
      code: VAL_EDU0105
      message: CPA cannot be null

# UserExperienceRequest
user_experience_request:
  experience_start_time:
    not_null:
      code: VAL_EXP0101
      message: Experience start time cannot be null
  experience_end_time:
    not_null:
      code: VAL_EXP0102
      message: Experience end time cannot be null
  experience_type:
    not_null:
      code: VAL_EXP0103
      message: Experience type cannot be null
  experience_type.constant_id:
    uuid_validation:
      code: VAL_EXP0104
      message: Experience type constant ID must be a valid UUID
  work_place:
    not_blank_string_validation:
      code: VAL_EXP0105
      message: Work place cannot be blank
  position:
    not_blank_string_validation:
      code: VAL_EXP0106
      message: Position cannot be blank

# UserAwardRequest
user_award_request:
  certificate_time:
    not_null:
      code: VAL_AWA0101
      message: Certificate time cannot be null
  certificate_name:
    not_blank_string_validation:
      code: VAL_AWA0102
      message: Certificate name cannot be blank

# OtherDescription
other_description:
  title:
    not_blank_string_validation:
      code: VAL_OTH0101
      message: Title cannot be blank
  description:
    not_blank_string_validation:
      code: VAL_OTH0102
      message: Description cannot be blank

# ApplicationPositionRequest
application_position_request:
  status:
    not_null:
      code: VAL_APP0101
      message: Status cannot be null
  apply_position:
    not_null:
      code: VAL_APP0102
      message: Apply position cannot be null
  apply_position.constant_id:
    uuid_validation:
      code: VAL_APP0103
      message: Apply position constant ID must be a valid UUID
  salary_range:
    not_null:
      code: VAL_APP0104
      message: Salary range cannot be null
  salary_range.constant_id:
    uuid_validation:
      code: VAL_APP0105
      message: Salary range constant ID must be a valid UUID
  skills:
    not_null:
      code: VAL_APP0106
      message: Skills cannot be null
    not_empty:
      code: VAL_APP0107
      message: Skills cannot be empty

# ApplicationSkillRequest
application_skill_request:
  note:
    not_blank_string_validation:
      code: VAL_SKI0101
      message: Note cannot be blank
  skill:
    not_null:
      code: VAL_SKI0102
      message: Skill cannot be null
  skill.constant_id:
    uuid_validation:
      code: VAL_SKI0103
      message: Skill constant ID must be a valid UUID

# CompanyProfileRequest
company_profile_request:
  account_status:
    not_null:
      code: VAL_COM0101
      message: Account status cannot be null
  address:
    not_blank_string_validation:
      code: VAL_COM0102
      message: Address cannot be blank
  phone_number:
    not_blank_string_validation:
      code: VAL_COM0103
      message: Phone number cannot be blank
  company_name:
    not_blank_string_validation:
      code: VAL_COM0104
      message: Company name cannot be blank
  company_url:
    not_blank_string_validation:
      code: VAL_COM0105
      message: Company URL cannot be blank
  established_date:
    not_null:
      code: VAL_COM0106
      message: Established date cannot be null

# LanguageRequest
language_request:
  language:
    not_null:
      code: VAL_LAN0101
      message: Language cannot be null
  language.constant_id:
    uuid_validation:
      code: VAL_LAN0102
      message: Language constant ID must be a valid UUID
  score:
    not_blank_string_validation:
      code: VAL_LAN0103
      message: Score cannot be null

# InterviewInvitationRequest
interview_invitation_request:
  matching_id:
    uuid_validation:
      code: VAL_INT0101
      message: Match ID must be a valid UUID
  interview_time:
    not_null:
      code: VAL_INT0102
      message: Interview time cannot be null
  interview_position_id:
    uuid_validation:
      code: VAL_INT0103
      message: Interview position ID must be a valid UUID
