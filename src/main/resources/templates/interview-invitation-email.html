<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title>Interview Invitation</title>
    <!--[if !mso]><!-- -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style type="text/css">
      #outlook a {
        padding: 0;
      }
      .ReadMsgBody {
        width: 100%;
      }
      .ExternalClass {
        width: 100%;
      }
      .ExternalClass * {
        line-height: 100%;
      }
      body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      table,
      td {
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }
    </style>
    <!--[if !mso]><!-->
    <style type="text/css">
      @media only screen and (max-width: 480px) {
        @-ms-viewport {
          width: 320px;
        }
        @viewport {
          width: 320px;
        }
      }
    </style>
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&display=swap"
      rel="stylesheet"
      type="text/css"
    />
    <style type="text/css">
      @import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&display=swap");
    </style>
    <!--<![endif]-->
    <style type="text/css">
      @media only screen and (max-width: 595px) {
        .container {
          width: 100% !important;
        }
        .button {
          display: block !important;
          width: auto !important;
        }
      }
      body {
        font-family: "Open Sans", sans-serif;
        background: #e5e5e5;
      }
    </style>
  </head>
  <body style="font-family: 'Open Sans', sans-serif; background: #e5e5e5">
    <table width="100%" cellspacing="0" cellpadding="0" border="0" align="center" bgcolor="#F6FAFB">
      <tbody>
        <tr>
          <td valign="top" align="center">
            <table class="container" width="600" cellspacing="0" cellpadding="0" border="0">
              <tbody>
                <tr>
                  <td style="padding: 48px 0 30px 0; text-align: center; font-size: 14px; color: #4c83ee">JOB SWIPE</td>
                </tr>
                <tr>
                  <td class="main-content" style="padding: 48px 30px 40px; color: #000000" bgcolor="#ffffff">
                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                      <tbody>
                        <tr>
                          <td
                            style="
                              padding: 0 0 24px 0;
                              font-size: 18px;
                              line-height: 150%;
                              font-weight: bold;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            Dear
                            <span
                              style="
                                padding: 0 0 24px 0;
                                font-size: 18px;
                                line-height: 150%;
                                font-weight: bold;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text="${candidate_name}"
                            ></span
                            >,
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              font-weight: 400;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            We are pleased to invite you to an interview for the
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                font-weight: 400;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text=" ${job_position} "
                            ></span>
                            position at
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                font-weight: 400;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text=" ${company_name}"
                            ></span
                            >. Your interview is scheduled as follows:
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            <strong>Date and Time:</strong>
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text="${interview_time}"
                            ></span>
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            The interview will be held at our office:
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            <strong>Address:</strong>
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text="${company_address}"
                            ></span>
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            Please bring a copy of your resume and any other relevant documents. If you have any
                            questions or need to reschedule, please contact us at
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text=" ${company_email} "
                            ></span>
                            or call us at
                            <span
                              style="
                                padding: 0 0 10px 0;
                                font-size: 14px;
                                line-height: 150%;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text=" ${company_phone} "
                            ></span
                            >.
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 10px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            This email was sent automatically by the Job Swipe.
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              padding: 0 0 60px 0;
                              font-size: 14px;
                              line-height: 150%;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            Best regards,<br /><span
                              style="
                                padding: 0 0 60px 0;
                                font-size: 14px;
                                line-height: 150%;
                                color: #000000;
                                letter-spacing: 0.01em;
                              "
                              th:text="${company_name}"
                            ></span>
                            Recruitment Team
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 0 0 16px">
                            <span style="display: block; width: 117px; border-bottom: 1px solid #8b949f"></span>
                          </td>
                        </tr>
                        <tr>
                          <td
                            style="
                              font-size: 14px;
                              line-height: 170%;
                              font-weight: 400;
                              color: #000000;
                              letter-spacing: 0.01em;
                            "
                          >
                            Best regards,<br /><strong>Job Swipe</strong>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 24px 0 48px; font-size: 0px">
                    <div
                      class="outlook-group-fix"
                      style="
                        padding: 0 0 20px 0;
                        vertical-align: top;
                        display: inline-block;
                        text-align: center;
                        width: 100%;
                      "
                    >
                      <span style="padding: 0; font-size: 11px; line-height: 15px; font-weight: normal; color: #8b949f">
                        <span
                          style="padding: 0; font-size: 11px; line-height: 15px; font-weight: normal; color: #8b949f"
                          th:text="${company_name}"
                        ></span
                        ><br />
                        <span
                          style="padding: 0; font-size: 11px; line-height: 15px; font-weight: normal; color: #8b949f"
                          th:text="${company_address}"
                        ></span
                        ><br />Phone:
                        <span
                          style="padding: 0; font-size: 11px; line-height: 15px; font-weight: normal; color: #8b949f"
                          th:text=" ${company_phone} "
                        ></span>
                        | Email:
                        <a href="mailto:${company_email}" style="color: #4c83ee">
                          <span style="color: #4c83ee" th:text=" ${company_email} "></span></a
                        ><br />
                        Sent via Job Swipe<br />Job Swipe, Da Nang, Viet Nam | Support Email:
                        <a href="mailto:<EMAIL>" style="color: #4c83ee"><EMAIL></a>
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
